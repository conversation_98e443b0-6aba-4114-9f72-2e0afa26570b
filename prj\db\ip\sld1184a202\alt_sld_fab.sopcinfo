<?xml version="1.0" encoding="UTF-8"?>
<EnsembleReport name="alt_sld_fab" kind="alt_sld_fab" version="1.0" fabric="QSYS">
 <!-- Format version 18.1 625 (Future versions may contain additional information.) -->
 <!-- 2025.07.17.15:34:10 -->
 <!-- A collection of modules and connections -->
 <parameter name="AUTO_DEVICE_FAMILY">
  <type>java.lang.String</type>
  <value>CYCLONEIVE</value>
  <derived>false</derived>
  <enabled>true</enabled>
  <visible>false</visible>
  <valid>true</valid>
  <sysinfo_type>DEVICE_FAMILY</sysinfo_type>
 </parameter>
 <parameter name="AUTO_DEVICE">
  <type>java.lang.String</type>
  <value></value>
  <derived>false</derived>
  <enabled>true</enabled>
  <visible>false</visible>
  <valid>true</valid>
  <sysinfo_type>DEVICE</sysinfo_type>
 </parameter>
 <parameter name="AUTO_DEVICE_SPEEDGRADE">
  <type>java.lang.String</type>
  <value></value>
  <derived>false</derived>
  <enabled>true</enabled>
  <visible>false</visible>
  <valid>true</valid>
  <sysinfo_type>DEVICE_SPEEDGRADE</sysinfo_type>
 </parameter>
 <parameter name="deviceFamily">
  <type>java.lang.String</type>
  <value>Cyclone IV E</value>
  <derived>false</derived>
  <enabled>true</enabled>
  <visible>false</visible>
  <valid>true</valid>
  <sysinfo_type>DEVICE_FAMILY</sysinfo_type>
 </parameter>
 <parameter name="generateLegacySim">
  <type>boolean</type>
  <value>false</value>
  <derived>false</derived>
  <enabled>true</enabled>
  <visible>true</visible>
  <valid>true</valid>
 </parameter>
 <module
   name="alt_sld_fab"
   kind="alt_sld_fab"
   version="18.1"
   path="alt_sld_fab">
  <!-- Describes a single module. Module parameters are
the requested settings for a module instance. -->
  <parameter name="DESIGN_HASH">
   <type>java.lang.String</type>
   <value>0c9bddac927be960db37</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="NODE_COUNT">
   <type>int</type>
   <value>1</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="MAX_WIDTH">
   <type>int</type>
   <value>33</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="SETTINGS">
   <type>java.lang.String</type>
   <value>{fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 psig 9b67919e} </value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="CLOCKS">
   <type>java.lang.String</type>
   <value>{id {} } </value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="AGENTS">
   <type>java.lang.String</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="EP_INFOS">
   <type>java.lang.String</type>
   <value>{hpath {sld_signaltap:auto_signaltap_0} } </value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="MIRROR">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="TOP_HUB">
   <type>int</type>
   <value>1</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="COMPOSED_SETTINGS">
   <type>java.lang.String</type>
   <value>{fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } </value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="DEVICE_FAMILY">
   <type>java.lang.String</type>
   <value>CYCLONEIVE</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
   <sysinfo_type>DEVICE_FAMILY</sysinfo_type>
  </parameter>
  <parameter name="AUTO_DEVICE">
   <type>java.lang.String</type>
   <value>Unknown</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>false</visible>
   <valid>true</valid>
   <sysinfo_type>DEVICE</sysinfo_type>
  </parameter>
  <parameter name="AUTO_DEVICE_SPEEDGRADE">
   <type>java.lang.String</type>
   <value>Unknown</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>false</visible>
   <valid>true</valid>
   <sysinfo_type>DEVICE_SPEEDGRADE</sysinfo_type>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <interface name="nodes" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>nodes_send</name>
    <direction>Input</direction>
    <width>33</width>
    <role>send</role>
   </port>
   <port>
    <name>nodes_receive</name>
    <direction>Output</direction>
    <width>33</width>
    <role>receive</role>
   </port>
  </interface>
  <interface name="hub" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <assignment>
    <name>debug.hostConnection</name>
    <value>type jtag connidx ext</value>
   </assignment>
   <assignment>
    <name>debug.providesServices</name>
    <value>deviceConn</value>
   </assignment>
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>hub_tck</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tck</role>
   </port>
   <port>
    <name>hub_tms</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tms</role>
   </port>
   <port>
    <name>hub_tdi</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tdi</role>
   </port>
   <port>
    <name>hub_tdo</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tdo</role>
   </port>
  </interface>
 </module>
 <module
   name="alt_sld_fab_presplit"
   kind="altera_super_splitter"
   version="18.1"
   path="alt_sld_fab.presplit">
  <!-- Describes a single module. Module parameters are
the requested settings for a module instance. -->
  <assignment>
   <name>debug.isTransparent</name>
   <value>true</value>
  </assignment>
  <parameter name="MAX_WIDTH">
   <type>int</type>
   <value>33</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="SEND_WIDTHS">
   <type>java.lang.String</type>
   <value>12</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="RECEIVE_WIDTHS">
   <type>java.lang.String</type>
   <value>33</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <interface name="nodes" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>send</name>
    <direction>Input</direction>
    <width>33</width>
    <role>send</role>
   </port>
   <port>
    <name>receive</name>
    <direction>Output</direction>
    <width>33</width>
    <role>receive</role>
   </port>
  </interface>
  <interface name="pass" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>send_0</name>
    <direction>Output</direction>
    <width>12</width>
    <role>send_0</role>
   </port>
   <port>
    <name>receive_0</name>
    <direction>Input</direction>
    <width>33</width>
    <role>receive_0</role>
   </port>
  </interface>
 </module>
 <module
   name="alt_sld_fab_splitter"
   kind="altera_sld_splitter"
   version="18.1"
   path="alt_sld_fab.splitter">
  <!-- Describes a single module. Module parameters are
the requested settings for a module instance. -->
  <assignment>
   <name>debug.isTransparent</name>
   <value>true</value>
  </assignment>
  <assignment>
   <name>debug.virtualInterface.link_0</name>
   <value>debug.endpointLink {fabric sld index 1} </value>
  </assignment>
  <parameter name="FRAGMENTS">
   <type>java.lang.String</type>
   <value>{{name clock type clock dir end ports { {tck clk in 1 0} } } {name node type conduit dir end ports { {tms tms in 1 1} {tdi tdi in 1 2} {tdo tdo out 1 0} {ena ena in 1 3} {usr1 usr1 in 1 4} {clr clr in 1 5} {clrn clrn in 1 6} {jtag_state_tlr jtag_state_tlr in 1 7} {jtag_state_rti jtag_state_rti in 1 8} {jtag_state_sdrs jtag_state_sdrs in 1 9} {jtag_state_cdr jtag_state_cdr in 1 10} {jtag_state_sdr jtag_state_sdr in 1 11} {jtag_state_e1dr jtag_state_e1dr in 1 12} {jtag_state_pdr jtag_state_pdr in 1 13} {jtag_state_e2dr jtag_state_e2dr in 1 14} {jtag_state_udr jtag_state_udr in 1 15} {jtag_state_sirs jtag_state_sirs in 1 16} {jtag_state_cir jtag_state_cir in 1 17} {jtag_state_sir jtag_state_sir in 1 18} {jtag_state_e1ir jtag_state_e1ir in 1 19} {jtag_state_pir jtag_state_pir in 1 20} {jtag_state_e2ir jtag_state_e2ir in 1 21} {jtag_state_uir jtag_state_uir in 1 22} {ir_in ir_in in 10 23} {irq irq out 1 1} {ir_out ir_out out 10 2} } clock clock assign {debug.controlledBy {link_0} } moduleassign {debug.virtualInterface.link_0 {debug.endpointLink {fabric sld index 1} } } } } </value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="EXAMPLE">
   <type>java.lang.String</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="ADD_INTERFACE_ASGN">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <interface name="nodes" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>send_0</name>
    <direction>Input</direction>
    <width>12</width>
    <role>send_0</role>
   </port>
   <port>
    <name>receive_0</name>
    <direction>Output</direction>
    <width>33</width>
    <role>receive_0</role>
   </port>
  </interface>
  <interface name="clock_0" kind="clock_sink" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="externallyDriven">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="ptfSchematicName">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>clock</type>
   <isStart>false</isStart>
   <port>
    <name>tck_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>clk</role>
   </port>
  </interface>
  <interface name="node_0" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <assignment>
    <name>debug.controlledBy</name>
    <value>link_0</value>
   </assignment>
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value>clock_0</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>tms_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tms</role>
   </port>
   <port>
    <name>tdi_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tdi</role>
   </port>
   <port>
    <name>tdo_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tdo</role>
   </port>
   <port>
    <name>ena_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>ena</role>
   </port>
   <port>
    <name>usr1_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>usr1</role>
   </port>
   <port>
    <name>clr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>clr</role>
   </port>
   <port>
    <name>clrn_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>clrn</role>
   </port>
   <port>
    <name>jtag_state_tlr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_tlr</role>
   </port>
   <port>
    <name>jtag_state_rti_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_rti</role>
   </port>
   <port>
    <name>jtag_state_sdrs_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_sdrs</role>
   </port>
   <port>
    <name>jtag_state_cdr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_cdr</role>
   </port>
   <port>
    <name>jtag_state_sdr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_sdr</role>
   </port>
   <port>
    <name>jtag_state_e1dr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_e1dr</role>
   </port>
   <port>
    <name>jtag_state_pdr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_pdr</role>
   </port>
   <port>
    <name>jtag_state_e2dr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_e2dr</role>
   </port>
   <port>
    <name>jtag_state_udr_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_udr</role>
   </port>
   <port>
    <name>jtag_state_sirs_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_sirs</role>
   </port>
   <port>
    <name>jtag_state_cir_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_cir</role>
   </port>
   <port>
    <name>jtag_state_sir_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_sir</role>
   </port>
   <port>
    <name>jtag_state_e1ir_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_e1ir</role>
   </port>
   <port>
    <name>jtag_state_pir_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_pir</role>
   </port>
   <port>
    <name>jtag_state_e2ir_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_e2ir</role>
   </port>
   <port>
    <name>jtag_state_uir_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>jtag_state_uir</role>
   </port>
   <port>
    <name>ir_in_0</name>
    <direction>Input</direction>
    <width>10</width>
    <role>ir_in</role>
   </port>
   <port>
    <name>irq_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>irq</role>
   </port>
   <port>
    <name>ir_out_0</name>
    <direction>Output</direction>
    <width>10</width>
    <role>ir_out</role>
   </port>
  </interface>
 </module>
 <module
   name="alt_sld_fab_jtagpins"
   kind="altera_jtag_pins_bridge"
   version="18.1"
   path="alt_sld_fab.jtagpins">
  <!-- Describes a single module. Module parameters are
the requested settings for a module instance. -->
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <interface name="pins" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <assignment>
    <name>debug.hostConnection</name>
    <value>type jtag connidx ext</value>
   </assignment>
   <assignment>
    <name>debug.providesServices</name>
    <value>deviceConn</value>
   </assignment>
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>ext_tck</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tck</role>
   </port>
   <port>
    <name>ext_tms</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tms</role>
   </port>
   <port>
    <name>ext_tdi</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tdi</role>
   </port>
   <port>
    <name>ext_tdo</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tdo</role>
   </port>
  </interface>
  <interface name="clock" kind="clock_source" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedDirectClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="clockRate">
    <type>long</type>
    <value>0</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="clockRateKnown">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="externallyDriven">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="ptfSchematicName">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>clock</type>
   <isStart>true</isStart>
   <port>
    <name>int_tck</name>
    <direction>Output</direction>
    <width>1</width>
    <role>clk</role>
   </port>
   <clockDomainMember>
    <isBridge>false</isBridge>
    <moduleName>alt_sld_fab_sldfabric</moduleName>
    <slaveName>clock</slaveName>
    <name>alt_sld_fab_sldfabric.clock</name>
   </clockDomainMember>
  </interface>
  <interface name="node" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <assignment>
    <name>debug.controlledBy</name>
    <value>pins</value>
   </assignment>
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value>clock</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>int_tms</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tms</role>
   </port>
   <port>
    <name>int_tdi</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tdi</role>
   </port>
   <port>
    <name>int_tdo</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tdo</role>
   </port>
  </interface>
 </module>
 <module
   name="alt_sld_fab_sldfabric"
   kind="altera_sld_jtag_hub"
   version="18.1"
   path="alt_sld_fab.sldfabric">
  <!-- Describes a single module. Module parameters are
the requested settings for a module instance. -->
  <parameter name="DEVICE_FAMILY">
   <type>java.lang.String</type>
   <value>CYCLONEIVE</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
   <sysinfo_type>DEVICE_FAMILY</sysinfo_type>
  </parameter>
  <parameter name="SETTINGS">
   <type>java.lang.String</type>
   <value>{mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } </value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="COUNT">
   <type>int</type>
   <value>1</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="N_SEL_BITS">
   <type>int</type>
   <value>1</value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="N_NODE_IR_BITS">
   <type>int</type>
   <value>10</value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="NODE_INFO">
   <type>java.lang.String</type>
   <value>00110000000000000110111000000000</value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="COMPILATION_MODE">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="BROADCAST_FEATURE">
   <type>int</type>
   <value>0</value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="FORCE_IR_CAPTURE_FEATURE">
   <type>int</type>
   <value>1</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="FORCE_PRE_1_4_FEATURE">
   <type>int</type>
   <value>0</value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="NEGEDGE_TDO_LATCH">
   <type>int</type>
   <value>1</value>
   <derived>true</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="ENABLE_SOFT_CORE_CONTROLLER">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="TOP_HUB">
   <type>int</type>
   <value>1</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="CONN_INDEX">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <interface name="clock" kind="clock_sink" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="externallyDriven">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="ptfSchematicName">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>clock</type>
   <isStart>false</isStart>
   <port>
    <name>jsm_tck</name>
    <direction>Input</direction>
    <width>1</width>
    <role>clk</role>
   </port>
  </interface>
  <interface name="node" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <assignment>
    <name>debug.connIndex</name>
    <value>0</value>
   </assignment>
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value>clock</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>jsm_tms</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tms</role>
   </port>
   <port>
    <name>hub_tdi</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tdi</role>
   </port>
   <port>
    <name>hub_tdo</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tdo</role>
   </port>
  </interface>
  <interface name="clock_0" kind="clock_source" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedDirectClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="clockRate">
    <type>long</type>
    <value>0</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="clockRateKnown">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="externallyDriven">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="ptfSchematicName">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>false</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>clock</type>
   <isStart>true</isStart>
   <port>
    <name>node_raw_tck_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>clk</role>
   </port>
   <clockDomainMember>
    <isBridge>false</isBridge>
    <moduleName>alt_sld_fab_splitter</moduleName>
    <slaveName>clock_0</slaveName>
    <name>alt_sld_fab_splitter.clock_0</name>
   </clockDomainMember>
  </interface>
  <interface name="node_0" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <assignment>
    <name>debug.controlledBy</name>
    <value>node</value>
   </assignment>
   <assignment>
    <name>debug.encodedSldId</name>
    <value>805334528</value>
   </assignment>
   <assignment>
    <name>debug.providesServices</name>
    <value>sld</value>
   </assignment>
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value>clock_0</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>node_ir_in_1d_0</name>
    <direction>Output</direction>
    <width>10</width>
    <role>ir_in</role>
   </port>
   <port>
    <name>node_ir_out_1d_0</name>
    <direction>Input</direction>
    <width>10</width>
    <role>ir_out</role>
   </port>
   <port>
    <name>node_tdo_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>tdo</role>
   </port>
   <port>
    <name>node_irq_0</name>
    <direction>Input</direction>
    <width>1</width>
    <role>irq</role>
   </port>
   <port>
    <name>node_raw_tms_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tms</role>
   </port>
   <port>
    <name>node_tdi_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>tdi</role>
   </port>
   <port>
    <name>node_clr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>clr</role>
   </port>
   <port>
    <name>node_clrn_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>clrn</role>
   </port>
   <port>
    <name>node_usr1_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>usr1</role>
   </port>
   <port>
    <name>node_ena_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>ena</role>
   </port>
   <port>
    <name>node_jtag_state_tlr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_tlr</role>
   </port>
   <port>
    <name>node_jtag_state_rti_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_rti</role>
   </port>
   <port>
    <name>node_jtag_state_sdrs_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_sdrs</role>
   </port>
   <port>
    <name>node_jtag_state_cdr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_cdr</role>
   </port>
   <port>
    <name>node_jtag_state_sdr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_sdr</role>
   </port>
   <port>
    <name>node_jtag_state_e1dr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_e1dr</role>
   </port>
   <port>
    <name>node_jtag_state_pdr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_pdr</role>
   </port>
   <port>
    <name>node_jtag_state_e2dr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_e2dr</role>
   </port>
   <port>
    <name>node_jtag_state_udr_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_udr</role>
   </port>
   <port>
    <name>node_jtag_state_sirs_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_sirs</role>
   </port>
   <port>
    <name>node_jtag_state_cir_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_cir</role>
   </port>
   <port>
    <name>node_jtag_state_sir_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_sir</role>
   </port>
   <port>
    <name>node_jtag_state_e1ir_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_e1ir</role>
   </port>
   <port>
    <name>node_jtag_state_pir_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_pir</role>
   </port>
   <port>
    <name>node_jtag_state_e2ir_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_e2ir</role>
   </port>
   <port>
    <name>node_jtag_state_uir_0</name>
    <direction>Output</direction>
    <width>1</width>
    <role>jtag_state_uir</role>
   </port>
  </interface>
  <interface name="ident" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>ident_writedata</name>
    <direction>Output</direction>
    <width>4</width>
    <role>writedata</role>
   </port>
   <port>
    <name>ident_address</name>
    <direction>Output</direction>
    <width>5</width>
    <role>address</role>
   </port>
   <port>
    <name>ident_readdata</name>
    <direction>Input</direction>
    <width>4</width>
    <role>readdata</role>
   </port>
  </interface>
 </module>
 <module
   name="alt_sld_fab_ident"
   kind="altera_connection_identification_hub"
   version="18.1"
   path="alt_sld_fab.ident">
  <!-- Describes a single module. Module parameters are
the requested settings for a module instance. -->
  <parameter name="DESIGN_HASH">
   <type>java.lang.String</type>
   <value>0c9bddac927be960db37</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="COUNT">
   <type>int</type>
   <value>1</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="SETTINGS">
   <type>java.lang.String</type>
   <value>{width 4 latency 0} </value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <interface name="ident_0" kind="conduit_end" version="18.1">
   <!-- The connection points exposed by a module instance for the
particular module parameters. Connection points and their
parameters are a RESULT of the module parameters. -->
   <parameter name="associatedClock">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="associatedReset">
    <type>java.lang.String</type>
    <value></value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="deviceFamily">
    <type>java.lang.String</type>
    <value>UNKNOWN</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <parameter name="generateLegacySim">
    <type>boolean</type>
    <value>false</value>
    <derived>false</derived>
    <enabled>true</enabled>
    <visible>true</visible>
    <valid>true</valid>
   </parameter>
   <type>conduit</type>
   <isStart>false</isStart>
   <port>
    <name>address_0</name>
    <direction>Input</direction>
    <width>5</width>
    <role>address</role>
   </port>
   <port>
    <name>contrib_0</name>
    <direction>Input</direction>
    <width>4</width>
    <role>writedata</role>
   </port>
   <port>
    <name>rdata_0</name>
    <direction>Output</direction>
    <width>4</width>
    <role>readdata</role>
   </port>
  </interface>
 </module>
 <connection
   name="alt_sld_fab_presplit.pass/alt_sld_fab_splitter.nodes"
   kind="conduit"
   version="18.1"
   start="alt_sld_fab_presplit.pass"
   end="alt_sld_fab_splitter.nodes">
  <parameter name="endPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="endPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="width">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <startModule>alt_sld_fab_presplit</startModule>
  <startConnectionPoint>pass</startConnectionPoint>
  <endModule>alt_sld_fab_splitter</endModule>
  <endConnectionPoint>nodes</endConnectionPoint>
 </connection>
 <connection
   name="alt_sld_fab_jtagpins.clock/alt_sld_fab_sldfabric.clock"
   kind="clock"
   version="18.1"
   start="alt_sld_fab_jtagpins.clock"
   end="alt_sld_fab_sldfabric.clock">
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <startModule>alt_sld_fab_jtagpins</startModule>
  <startConnectionPoint>clock</startConnectionPoint>
  <endModule>alt_sld_fab_sldfabric</endModule>
  <endConnectionPoint>clock</endConnectionPoint>
 </connection>
 <connection
   name="alt_sld_fab_jtagpins.node/alt_sld_fab_sldfabric.node"
   kind="conduit"
   version="18.1"
   start="alt_sld_fab_jtagpins.node"
   end="alt_sld_fab_sldfabric.node">
  <parameter name="endPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="endPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="width">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <startModule>alt_sld_fab_jtagpins</startModule>
  <startConnectionPoint>node</startConnectionPoint>
  <endModule>alt_sld_fab_sldfabric</endModule>
  <endConnectionPoint>node</endConnectionPoint>
 </connection>
 <connection
   name="alt_sld_fab_sldfabric.clock_0/alt_sld_fab_splitter.clock_0"
   kind="clock"
   version="18.1"
   start="alt_sld_fab_sldfabric.clock_0"
   end="alt_sld_fab_splitter.clock_0">
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <startModule>alt_sld_fab_sldfabric</startModule>
  <startConnectionPoint>clock_0</startConnectionPoint>
  <endModule>alt_sld_fab_splitter</endModule>
  <endConnectionPoint>clock_0</endConnectionPoint>
 </connection>
 <connection
   name="alt_sld_fab_sldfabric.node_0/alt_sld_fab_splitter.node_0"
   kind="conduit"
   version="18.1"
   start="alt_sld_fab_sldfabric.node_0"
   end="alt_sld_fab_splitter.node_0">
  <parameter name="endPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="endPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="width">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <startModule>alt_sld_fab_sldfabric</startModule>
  <startConnectionPoint>node_0</startConnectionPoint>
  <endModule>alt_sld_fab_splitter</endModule>
  <endConnectionPoint>node_0</endConnectionPoint>
 </connection>
 <connection
   name="alt_sld_fab_sldfabric.ident/alt_sld_fab_ident.ident_0"
   kind="conduit"
   version="18.1"
   start="alt_sld_fab_sldfabric.ident"
   end="alt_sld_fab_ident.ident_0">
  <parameter name="endPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="endPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPort">
   <type>com.altera.entityinterfaces.IPort</type>
   <value></value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="startPortLSB">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="width">
   <type>int</type>
   <value>0</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="deviceFamily">
   <type>java.lang.String</type>
   <value>UNKNOWN</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <parameter name="generateLegacySim">
   <type>boolean</type>
   <value>false</value>
   <derived>false</derived>
   <enabled>true</enabled>
   <visible>true</visible>
   <valid>true</valid>
  </parameter>
  <startModule>alt_sld_fab_sldfabric</startModule>
  <startConnectionPoint>ident</startConnectionPoint>
  <endModule>alt_sld_fab_ident</endModule>
  <endConnectionPoint>ident_0</endConnectionPoint>
 </connection>
 <plugin>
  <instanceCount>1</instanceCount>
  <name>alt_sld_fab</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IModule</subtype>
  <displayName>Top level generated instrumentation fabric</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>12</instanceCount>
  <name>conduit_end</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IMutableConnectionPoint</subtype>
  <displayName>Conduit</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>1</instanceCount>
  <name>altera_super_splitter</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IModule</subtype>
  <displayName>Splitter for debug fabric</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>1</instanceCount>
  <name>altera_sld_splitter</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IModule</subtype>
  <displayName>Splitter for debug fabric</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>2</instanceCount>
  <name>clock_sink</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IMutableConnectionPoint</subtype>
  <displayName>Clock Input</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>1</instanceCount>
  <name>altera_jtag_pins_bridge</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IModule</subtype>
  <displayName>altera_jtag_pins_bridge</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>2</instanceCount>
  <name>clock_source</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IMutableConnectionPoint</subtype>
  <displayName>Clock Output</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>1</instanceCount>
  <name>altera_sld_jtag_hub</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IModule</subtype>
  <displayName>Altera SLD JTAG Hub</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>1</instanceCount>
  <name>altera_connection_identification_hub</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IModule</subtype>
  <displayName>Connection identification hub</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>4</instanceCount>
  <name>conduit</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IConnection</subtype>
  <displayName>Conduit Connection</displayName>
  <version>18.1</version>
 </plugin>
 <plugin>
  <instanceCount>2</instanceCount>
  <name>clock</name>
  <type>com.altera.entityinterfaces.IElementClass</type>
  <subtype>com.altera.entityinterfaces.IConnection</subtype>
  <displayName>Clock Connection</displayName>
  <version>18.1</version>
 </plugin>
 <reportVersion>18.1 625</reportVersion>
 <uniqueIdentifier></uniqueIdentifier>
</EnsembleReport>
