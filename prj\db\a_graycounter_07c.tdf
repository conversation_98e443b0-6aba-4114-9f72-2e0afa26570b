--a_graycounter DEVICE_FAMILY="Cyclone IV E" PVALUE=1 WIDTH=11 clock cnt_en q ALTERA_INTERNAL_OPTIONS=suppress_da_rule_internal=S102
--VERSION_BEGIN 18.1 cbx_a_gray2bin 2018:09:12:13:04:24:SJ cbx_a_graycounter 2018:09:12:13:04:24:SJ cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.



--synthesis_resources = reg 15 
OPTIONS ALTERA_INTERNAL_OPTION = "suppress_da_rule_internal=S102;{-to counter8a0} POWER_UP_LEVEL=HIGH;{-to parity9} POWER_UP_LEVEL=HIGH";

SUBDESIGN a_graycounter_07c
( 
	clock	:	input;
	cnt_en	:	input;
	q[10..0]	:	output;
) 
VARIABLE 
	counter8a0 : dffeas
		WITH (
			power_up = "high"
		);
	counter8a1 : dffeas;
	counter8a2 : dffeas;
	counter8a3 : dffeas;
	counter8a4 : dffeas;
	counter8a5 : dffeas;
	counter8a6 : dffeas;
	counter8a7 : dffeas;
	counter8a8 : dffeas;
	counter8a9 : dffeas;
	counter8a10 : dffeas;
	parity9 : dffeas
		WITH (
			power_up = "high"
		);
	sub_parity10a[2..0] : dffeas;
	cntr_cout[10..0]	: WIRE;
	parity_cout	: WIRE;
	sclr	: NODE;
	updown	: NODE;

BEGIN 
	counter8a[10..0].clk = clock;
	counter8a[10..0].d = ( (counter8a[10].q $ cntr_cout[9..9]), (counter8a[9].q $ (counter8a[8].q & cntr_cout[8..8])), (counter8a[8].q $ (counter8a[7].q & cntr_cout[7..7])), (counter8a[7].q $ (counter8a[6].q & cntr_cout[6..6])), (counter8a[6].q $ (counter8a[5].q & cntr_cout[5..5])), (counter8a[5].q $ (counter8a[4].q & cntr_cout[4..4])), (counter8a[4].q $ (counter8a[3].q & cntr_cout[3..3])), (counter8a[3].q $ (counter8a[2].q & cntr_cout[2..2])), (counter8a[2].q $ (counter8a[1].q & cntr_cout[1..1])), (counter8a[1].q $ (counter8a[0].q & cntr_cout[0..0])), ((cnt_en & (counter8a[0].q $ (! parity_cout))) # ((! cnt_en) & counter8a[0].q)));
	counter8a[10..0].sclr = sclr;
	parity9.clk = clock;
	parity9.d = ((cnt_en & ((sub_parity10a[0..0].q $ sub_parity10a[1..1].q) $ sub_parity10a[2..2].q)) # ((! cnt_en) & parity9.q));
	parity9.sclr = sclr;
	sub_parity10a[].clk = ( clock, clock, clock);
	sub_parity10a[].d = ( ((cnt_en & ((counter8a[8..8].q $ counter8a[9..9].q) $ counter8a[10..10].q)) # ((! cnt_en) & sub_parity10a[2].q)), ((cnt_en & (((counter8a[4..4].q $ counter8a[5..5].q) $ counter8a[6..6].q) $ counter8a[7..7].q)) # ((! cnt_en) & sub_parity10a[1].q)), ((cnt_en & (((counter8a[0..0].q $ counter8a[1..1].q) $ counter8a[2..2].q) $ counter8a[3..3].q)) # ((! cnt_en) & sub_parity10a[0].q)));
	sub_parity10a[].sclr = ( sclr, sclr, sclr);
	cntr_cout[] = ( B"0", (cntr_cout[8..8] & (! counter8a[8].q)), (cntr_cout[7..7] & (! counter8a[7].q)), (cntr_cout[6..6] & (! counter8a[6].q)), (cntr_cout[5..5] & (! counter8a[5].q)), (cntr_cout[4..4] & (! counter8a[4].q)), (cntr_cout[3..3] & (! counter8a[3].q)), (cntr_cout[2..2] & (! counter8a[2].q)), (cntr_cout[1..1] & (! counter8a[1].q)), (cntr_cout[0..0] & (! counter8a[0].q)), (cnt_en & parity_cout));
	parity_cout = (((! parity9.q) $ updown) & cnt_en);
	q[] = counter8a[10..0].q;
	sclr = GND;
	updown = VCC;
END;
--VALID FILE
