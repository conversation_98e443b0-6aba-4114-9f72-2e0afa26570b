v1
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1|mult_cft:auto_generated|mac_out4,
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1|mult_cft:auto_generated|mac_out2,
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1|mult_cft:auto_generated|mac_out4,
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1|mult_cft:auto_generated|mac_out2,
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0|mult_4dt:auto_generated|mac_out2,
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,voltage_scaler_clocked_fast:inst14|lpm_mult:Mult0|mult_4dt:auto_generated|mac_out2,
PORT_SWAPPING,PORT_SWAPPING_FINISHED,voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1|mult_cft:auto_generated|mac_mult3,
IO_RULES,NUM_PINS_NOT_EXCEED_LOC_AVAILABLE,PASS,IO_000001,Capacity Checks,Number of pins in an I/O bank should not exceed the number of locations available.,Critical,0 such failures found.,,I/O,,
IO_RULES,NUM_CLKS_NOT_EXCEED_CLKS_AVAILABLE,INAPPLICABLE,IO_000002,Capacity Checks,Number of clocks in an I/O bank should not exceed the number of clocks available.,Critical,No Global Signal assignments found.,,I/O,,
IO_RULES,NUM_VREF_NOT_EXCEED_LOC_AVAILABLE,PASS,IO_000003,Capacity Checks,Number of pins in a Vrefgroup should not exceed the number of locations available.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_BANK_SUPPORT_VCCIO,INAPPLICABLE,IO_000004,Voltage Compatibility Checks,The I/O bank should support the requested VCCIO.,Critical,No IOBANK_VCCIO assignments found.,,I/O,,
IO_RULES,IO_BANK_NOT_HAVE_COMPETING_VREF,INAPPLICABLE,IO_000005,Voltage Compatibility Checks,The I/O bank should not have competing VREF values.,Critical,No VREF I/O Standard assignments found.,,I/O,,
IO_RULES,IO_BANK_NOT_HAVE_COMPETING_VCCIO,PASS,IO_000006,Voltage Compatibility Checks,The I/O bank should not have competing VCCIO values.,Critical,0 such failures found.,,I/O,,
IO_RULES,CHECK_UNAVAILABLE_LOC,PASS,IO_000007,Valid Location Checks,Checks for unavailable locations.,Critical,0 such failures found.,,I/O,,
IO_RULES,CHECK_RESERVED_LOC,INAPPLICABLE,IO_000008,Valid Location Checks,Checks for reserved locations.,Critical,No reserved LogicLock region found.,,I/O,,
IO_RULES,LOC_SUPPORT_IO_STD,PASS,IO_000009,I/O Properties Checks for One I/O,The location should support the requested I/O standard.,Critical,0 such failures found.,,I/O,,
IO_RULES,LOC_SUPPORT_IO_DIR,PASS,IO_000010,I/O Properties Checks for One I/O,The location should support the requested I/O direction.,Critical,0 such failures found.,,I/O,,
IO_RULES,LOC_SUPPORT_CURRENT_STRENGTH,INAPPLICABLE,IO_000011,I/O Properties Checks for One I/O,The location should support the requested Current Strength.,Critical,No Current Strength assignments found.,,I/O,,
IO_RULES,LOC_SUPPORT_OCT_VALUE,PASS,IO_000012,I/O Properties Checks for One I/O,The location should support the requested On Chip Termination value.,Critical,0 such failures found.,,I/O,,
IO_RULES,LOC_SUPPORT_BUS_HOLD_VALUE,INAPPLICABLE,IO_000013,I/O Properties Checks for One I/O,The location should support the requested Bus Hold value.,Critical,No Enable Bus-Hold Circuitry assignments found.,,I/O,,
IO_RULES,LOC_SUPPORT_WEAK_PULL_UP_VALUE,INAPPLICABLE,IO_000014,I/O Properties Checks for One I/O,The location should support the requested Weak Pull Up value.,Critical,No Weak Pull-Up Resistor assignments found.,,I/O,,
IO_RULES,LOC_SUPPORT_PCI_CLAMP_DIODE,PASS,IO_000015,I/O Properties Checks for One I/O,The location should support the requested PCI Clamp Diode.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_CURRENT_STRENGTH,INAPPLICABLE,IO_000018,I/O Properties Checks for One I/O,The I/O standard should support the requested Current Strength.,Critical,No Current Strength assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_OCT_VALUE,PASS,IO_000019,I/O Properties Checks for One I/O,The I/O standard should support the requested On Chip Termination value.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_PCI_CLAMP_DIODE,PASS,IO_000020,I/O Properties Checks for One I/O,The I/O standard should support the requested PCI Clamp Diode.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_WEAK_PULL_UP_VALUE,INAPPLICABLE,IO_000021,I/O Properties Checks for One I/O,The I/O standard should support the requested Weak Pull Up value.,Critical,No Weak Pull-Up Resistor assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_BUS_HOLD_VALUE,INAPPLICABLE,IO_000022,I/O Properties Checks for One I/O,The I/O standard should support the requested Bus Hold value.,Critical,No Enable Bus-Hold Circuitry assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_OPEN_DRAIN_VALUE,INAPPLICABLE,IO_000023,I/O Properties Checks for One I/O,The I/O standard should support the Open Drain value.,Critical,No open drain assignments found.,,I/O,,
IO_RULES,IO_DIR_SUPPORT_OCT_VALUE,PASS,IO_000024,I/O Properties Checks for One I/O,The I/O direction should support the On Chip Termination value.,Critical,0 such failures found.,,I/O,,
IO_RULES,OCT_AND_CURRENT_STRENGTH_NOT_USED_SIMULTANEOUSLY,INAPPLICABLE,IO_000026,I/O Properties Checks for One I/O,On Chip Termination and Current Strength should not be used at the same time.,Critical,No Current Strength assignments found.,,I/O,,
IO_RULES,WEAK_PULL_UP_AND_BUS_HOLD_NOT_USED_SIMULTANEOUSLY,INAPPLICABLE,IO_000027,I/O Properties Checks for One I/O,Weak Pull Up and Bus Hold should not be used at the same time.,Critical,No Enable Bus-Hold Circuitry or Weak Pull-Up Resistor assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORTS_SLEW_RATE,INAPPLICABLE,IO_000045,I/O Properties Checks for One I/O,The I/O standard should support the requested Slew Rate value.,Critical,No Slew Rate assignments found.,,I/O,,
IO_RULES,LOC_SUPPORTS_SLEW_RATE,INAPPLICABLE,IO_000046,I/O Properties Checks for One I/O,The location should support the requested Slew Rate value.,Critical,No Slew Rate assignments found.,,I/O,,
IO_RULES,OCT_SUPPORTS_SLEW_RATE,INAPPLICABLE,IO_000047,I/O Properties Checks for One I/O,On Chip Termination and Slew Rate should not be used at the same time.,Critical,No Slew Rate assignments found.,,I/O,,
IO_RULES,CURRENT_DENSITY_FOR_CONSECUTIVE_IO_NOT_EXCEED_CURRENT_VALUE,PASS,IO_000033,Electromigration Checks,Current density for consecutive I/Os should not exceed 240mA for row I/Os and 240mA for column I/Os.,Critical,0 such failures found.,,I/O,,
IO_RULES,SINGLE_ENDED_OUTPUTS_LAB_ROWS_FROM_DIFF_IO,INAPPLICABLE,IO_000034,SI Related Distance Checks,Single-ended outputs should be 5 LAB row(s) away from a differential I/O.,High,No Differential I/O Standard assignments found.,,I/O,,
IO_RULES,MAX_20_OUTPUTS_ALLOWED_IN_VREFGROUP,INAPPLICABLE,IO_000042,SI Related SSO Limit Checks,No more than 20 outputs are allowed in a VREF group when VREF is being read from.,High,No VREF I/O Standard assignments found.,,I/O,,
IO_RULES,DEV_IO_RULE_OCT_DISCLAIMER,,,,,,,,,,
IO_RULES_MATRIX,Pin/Rules,IO_000001;IO_000002;IO_000003;IO_000004;IO_000005;IO_000006;IO_000007;IO_000008;IO_000009;IO_000010;IO_000011;IO_000012;IO_000013;IO_000014;IO_000015;IO_000018;IO_000019;IO_000020;IO_000021;IO_000022;IO_000023;IO_000024;IO_000026;IO_000027;IO_000045;IO_000046;IO_000047;IO_000033;IO_000034;IO_000042,
IO_RULES_MATRIX,Total Pass,80;0;80;0;0;80;80;0;80;80;0;48;0;0;48;0;48;48;0;0;0;48;0;0;0;0;0;80;0;0,
IO_RULES_MATRIX,Total Unchecked,0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0,
IO_RULES_MATRIX,Total Inapplicable,0;80;0;80;80;0;0;80;0;0;80;32;80;80;32;80;32;32;80;80;80;32;80;80;80;80;80;0;80;80,
IO_RULES_MATRIX,Total Fail,0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0,
IO_RULES_MATRIX,DA1_OUTCLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUTCLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_OUTCLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_OUTCLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[13],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[12],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[11],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[10],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[9],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[8],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[7],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[6],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[5],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[4],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[3],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA1_OUT[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[13],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[12],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[11],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[10],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[9],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[8],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[7],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[6],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[5],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[4],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[3],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DA2_OUT[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[15],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[14],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[13],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[12],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[11],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[10],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[9],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[8],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[7],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[6],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[5],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[4],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[3],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_DB[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_NL_NADV,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_WR_NWE,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_CS_NEL,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,CLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,RST,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,FPGA_RD_NOE,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT_CLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT_CLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[3],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[3],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[4],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[4],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[5],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[5],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[6],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[6],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[7],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[7],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[8],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[8],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[9],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[9],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[10],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[10],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD1_INPUT[11],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,AD2_INPUT[11],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_SUMMARY,Total I/O Rules,30,
IO_RULES_SUMMARY,Number of I/O Rules Passed,12,
IO_RULES_SUMMARY,Number of I/O Rules Failed,0,
IO_RULES_SUMMARY,Number of I/O Rules Unchecked,0,
IO_RULES_SUMMARY,Number of I/O Rules Inapplicable,18,
