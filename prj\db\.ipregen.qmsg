{ "Info" "IQEXE_TCL_SCRIPT_STATUS" "d:/intelfpga/18.1/quartus/common/tcl/internal/ip_regen/ip_regen.tcl " "Evaluation of Tcl script d:/intelfpga/18.1/quartus/common/tcl/internal/ip_regen/ip_regen.tcl was successful" {  } {  } 0 23030 "Evaluation of Tcl script %1!s! was successful" 0 0 "Shell" 0 -1 1753807113902 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Shell 0 s 0 s Quartus Prime " "Quartus Prime Shell was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4864 " "Peak virtual memory: 4864 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753807113902 ""} { "Info" "IQEXE_END_BANNER_TIME" "Wed Jul 30 00:38:33 2025 " "Processing ended: Wed Jul 30 00:38:33 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753807113902 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:05 " "Elapsed time: 00:00:05" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753807113902 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:14 " "Total CPU time (on all processors): 00:00:14" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753807113902 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Shell" 0 -1 1753807113902 ""}
