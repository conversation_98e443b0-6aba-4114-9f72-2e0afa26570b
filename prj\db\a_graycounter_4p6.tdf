--a_graycounter DEVICE_FAMILY="Cyclone IV E" PVALUE=1 WIDTH=11 clock cnt_en q
--VERSION_BEGIN 18.1 cbx_a_gray2bin 2018:09:12:13:04:24:SJ cbx_a_graycounter 2018:09:12:13:04:24:SJ cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.



--synthesis_resources = reg 15 
OPTIONS ALTERA_INTERNAL_OPTION = "{-to counter5a0} POWER_UP_LEVEL=HIGH;{-to parity6} POWER_UP_LEVEL=HIGH";

SUBDESIGN a_graycounter_4p6
( 
	clock	:	input;
	cnt_en	:	input;
	q[10..0]	:	output;
) 
VARIABLE 
	counter5a0 : dffeas
		WITH (
			power_up = "high"
		);
	counter5a1 : dffeas;
	counter5a2 : dffeas;
	counter5a3 : dffeas;
	counter5a4 : dffeas;
	counter5a5 : dffeas;
	counter5a6 : dffeas;
	counter5a7 : dffeas;
	counter5a8 : dffeas;
	counter5a9 : dffeas;
	counter5a10 : dffeas;
	parity6 : dffeas
		WITH (
			power_up = "high"
		);
	sub_parity7a[2..0] : dffeas;
	cntr_cout[10..0]	: WIRE;
	parity_cout	: WIRE;
	sclr	: NODE;
	updown	: NODE;

BEGIN 
	counter5a[10..0].clk = clock;
	counter5a[10..0].d = ( (counter5a[10].q $ cntr_cout[9..9]), (counter5a[9].q $ (counter5a[8].q & cntr_cout[8..8])), (counter5a[8].q $ (counter5a[7].q & cntr_cout[7..7])), (counter5a[7].q $ (counter5a[6].q & cntr_cout[6..6])), (counter5a[6].q $ (counter5a[5].q & cntr_cout[5..5])), (counter5a[5].q $ (counter5a[4].q & cntr_cout[4..4])), (counter5a[4].q $ (counter5a[3].q & cntr_cout[3..3])), (counter5a[3].q $ (counter5a[2].q & cntr_cout[2..2])), (counter5a[2].q $ (counter5a[1].q & cntr_cout[1..1])), (counter5a[1].q $ (counter5a[0].q & cntr_cout[0..0])), ((cnt_en & (counter5a[0].q $ (! parity_cout))) # ((! cnt_en) & counter5a[0].q)));
	counter5a[10..0].sclr = sclr;
	parity6.clk = clock;
	parity6.d = ((cnt_en & ((sub_parity7a[0..0].q $ sub_parity7a[1..1].q) $ sub_parity7a[2..2].q)) # ((! cnt_en) & parity6.q));
	parity6.sclr = sclr;
	sub_parity7a[].clk = ( clock, clock, clock);
	sub_parity7a[].d = ( ((cnt_en & ((counter5a[8..8].q $ counter5a[9..9].q) $ counter5a[10..10].q)) # ((! cnt_en) & sub_parity7a[2].q)), ((cnt_en & (((counter5a[4..4].q $ counter5a[5..5].q) $ counter5a[6..6].q) $ counter5a[7..7].q)) # ((! cnt_en) & sub_parity7a[1].q)), ((cnt_en & (((counter5a[0..0].q $ counter5a[1..1].q) $ counter5a[2..2].q) $ counter5a[3..3].q)) # ((! cnt_en) & sub_parity7a[0].q)));
	sub_parity7a[].sclr = ( sclr, sclr, sclr);
	cntr_cout[] = ( B"0", (cntr_cout[8..8] & (! counter5a[8].q)), (cntr_cout[7..7] & (! counter5a[7].q)), (cntr_cout[6..6] & (! counter5a[6].q)), (cntr_cout[5..5] & (! counter5a[5].q)), (cntr_cout[4..4] & (! counter5a[4].q)), (cntr_cout[3..3] & (! counter5a[3].q)), (cntr_cout[2..2] & (! counter5a[2].q)), (cntr_cout[1..1] & (! counter5a[1].q)), (cntr_cout[0..0] & (! counter5a[0].q)), (cnt_en & parity_cout));
	parity_cout = (((! parity6.q) $ updown) & cnt_en);
	q[] = counter5a[10..0].q;
	sclr = GND;
	updown = VCC;
END;
--VALID FILE
