/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 288 128)
	(text "DA_WAVEFORM" (rect 5 0 89 12)(font "Arial" ))
	(text "inst" (rect 8 96 20 108)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CLK" (rect 0 0 20 12)(font "Arial" ))
		(text "CLK" (rect 21 27 41 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "WAVE_SEL[7..0]" (rect 0 0 79 12)(font "Arial" ))
		(text "WAVE_SEL[7..0]" (rect 21 43 100 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 3))
	)
	(port
		(pt 0 64)
		(input)
		(text "PHASE_ADDR[7..0]" (rect 0 0 90 12)(font "Arial" ))
		(text "PHASE_ADDR[7..0]" (rect 21 59 111 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 272 32)
		(output)
		(text "WAVE_OUT[13..0]" (rect 0 0 83 12)(font "Arial" ))
		(text "WAVE_OUT[13..0]" (rect 168 27 251 39)(font "Arial" ))
		(line (pt 272 32)(pt 256 32)(line_width 3))
	)
	(parameter
		"SINE_WAVE"
		"00000000"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"SQUARE_WAVE"
		"00000001"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"TRIANGLE_WAVE"
		"00000010"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"SAWTOOTH_WAVE"
		"00000011"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(drawing
		(rectangle (rect 16 16 256 96)(line_width 1))
	)
	(annotation_block (parameter)(rect 288 -64 388 16))
)
