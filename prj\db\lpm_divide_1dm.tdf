--lpm_divide DEVICE_FAMILY="Cyclone V" LPM_DREPRESENTATION="UNSIGNED" LPM_NREPRESENTATION="UNSIGNED" LPM_WIDTHD=12 LPM_WIDTHN=32 OPTIMIZE_FOR_SPEED=5 denom numer quotient CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48 IGNORE_CARRY_BUFFERS="OFF"
--VERSION_BEGIN 18.1 cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_abs 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_divide 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ cbx_util_mgl 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION sign_div_unsign_7nh (denominator[11..0], numerator[31..0])
RETURNS ( quotient[31..0], remainder[11..0]);

--synthesis_resources = 
SUBDESIGN lpm_divide_1dm
( 
	denom[11..0]	:	input;
	numer[31..0]	:	input;
	quotient[31..0]	:	output;
	remain[11..0]	:	output;
) 
VARIABLE 
	divider : sign_div_unsign_7nh;
	numer_tmp[31..0]	: WIRE;

BEGIN 
	divider.denominator[] = denom[];
	divider.numerator[] = numer_tmp[];
	numer_tmp[] = numer[];
	quotient[] = divider.quotient[];
	remain[] = divider.remainder[];
END;
--VALID FILE
