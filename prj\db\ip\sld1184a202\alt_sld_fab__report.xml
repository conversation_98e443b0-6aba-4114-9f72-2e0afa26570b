<?xml version="1.0" encoding="UTF-8"?>
<deploy
 date="2025.07.17.15:34:14"
 outputDirectory="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/">
 <perimeter>
  <parameter
     name="AUTO_DEVICE_FAMILY"
     type="String"
     defaultValue=""
     onHdl="0"
     affectsHdl="1" />
  <parameter
     name="AUTO_DEVICE"
     type="String"
     defaultValue=""
     onHdl="0"
     affectsHdl="1" />
  <parameter
     name="AUTO_DEVICE_SPEEDGRADE"
     type="String"
     defaultValue=""
     onHdl="0"
     affectsHdl="1" />
  <interface name="nodes" kind="conduit" start="0">
   <property name="associatedClock" value="" />
   <property name="associatedReset" value="" />
   <port name="nodes_send" direction="input" role="send" width="33" />
   <port name="nodes_receive" direction="output" role="receive" width="33" />
  </interface>
  <interface name="hub" kind="conduit" start="0">
   <property name="associatedClock" value="" />
   <property name="associatedReset" value="" />
   <port name="hub_tck" direction="input" role="tck" width="1" />
   <port name="hub_tms" direction="input" role="tms" width="1" />
   <port name="hub_tdi" direction="input" role="tdi" width="1" />
   <port name="hub_tdo" direction="output" role="tdo" width="1" />
  </interface>
 </perimeter>
 <entity
   path=""
   parameterizationKey="alt_sld_fab:1.0:AUTO_DEVICE=,AUTO_DEVICE_FAMILY=Cyclone IV E,AUTO_DEVICE_SPEEDGRADE=(alt_sld_fab:18.1:AGENTS=,AUTO_DEVICE=Unknown,AUTO_DEVICE_SPEEDGRADE=Unknown,CLOCKS={id {} } ,COMPOSED_SETTINGS={fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } ,DESIGN_HASH=0c9bddac927be960db37,DEVICE_FAMILY=Cyclone IV E,EP_INFOS={hpath {sld_signaltap:auto_signaltap_0} } ,MAX_WIDTH=33,MIRROR=0,NODE_COUNT=1,SETTINGS={fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 psig 9b67919e} ,TOP_HUB=1(altera_super_splitter:18.1:MAX_WIDTH=33,RECEIVE_WIDTHS=33,SEND_WIDTHS=12)(altera_sld_splitter:18.1:ADD_INTERFACE_ASGN=0,EXAMPLE=,FRAGMENTS={{name clock type clock dir end ports { {tck clk in 1 0} } } {name node type conduit dir end ports { {tms tms in 1 1} {tdi tdi in 1 2} {tdo tdo out 1 0} {ena ena in 1 3} {usr1 usr1 in 1 4} {clr clr in 1 5} {clrn clrn in 1 6} {jtag_state_tlr jtag_state_tlr in 1 7} {jtag_state_rti jtag_state_rti in 1 8} {jtag_state_sdrs jtag_state_sdrs in 1 9} {jtag_state_cdr jtag_state_cdr in 1 10} {jtag_state_sdr jtag_state_sdr in 1 11} {jtag_state_e1dr jtag_state_e1dr in 1 12} {jtag_state_pdr jtag_state_pdr in 1 13} {jtag_state_e2dr jtag_state_e2dr in 1 14} {jtag_state_udr jtag_state_udr in 1 15} {jtag_state_sirs jtag_state_sirs in 1 16} {jtag_state_cir jtag_state_cir in 1 17} {jtag_state_sir jtag_state_sir in 1 18} {jtag_state_e1ir jtag_state_e1ir in 1 19} {jtag_state_pir jtag_state_pir in 1 20} {jtag_state_e2ir jtag_state_e2ir in 1 21} {jtag_state_uir jtag_state_uir in 1 22} {ir_in ir_in in 10 23} {irq irq out 1 1} {ir_out ir_out out 10 2} } clock clock assign {debug.controlledBy {link_0} } moduleassign {debug.virtualInterface.link_0 {debug.endpointLink {fabric sld index 1} } } } } )(altera_jtag_pins_bridge:18.1:)(altera_sld_jtag_hub:18.1:BROADCAST_FEATURE=0,COMPILATION_MODE=0,CONN_INDEX=0,COUNT=1,DEVICE_FAMILY=Cyclone IV E,ENABLE_SOFT_CORE_CONTROLLER=0,FORCE_IR_CAPTURE_FEATURE=1,FORCE_PRE_1_4_FEATURE=0,NEGEDGE_TDO_LATCH=1,NODE_INFO=00110000000000000110111000000000,N_NODE_IR_BITS=10,N_SEL_BITS=1,SETTINGS={mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } ,TOP_HUB=1)(altera_connection_identification_hub:18.1:COUNT=1,DESIGN_HASH=0c9bddac927be960db37,SETTINGS={width 4 latency 0} )(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)(clock:18.1:)(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)(clock:18.1:)(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0))"
   instancePathKey="alt_sld_fab"
   kind="alt_sld_fab"
   version="1.0"
   name="alt_sld_fab">
  <parameter name="AUTO_DEVICE" value="" />
  <parameter name="AUTO_DEVICE_FAMILY" value="Cyclone IV E" />
  <parameter name="AUTO_DEVICE_SPEEDGRADE" value="" />
  <generatedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/alt_sld_fab.v"
       type="VERILOG" />
  </generatedFiles>
  <childGeneratedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab.v"
       type="VERILOG" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_presplit.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_splitter.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd"
       type="VHDL"
       attributes="" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_ident.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
  </childGeneratedFiles>
  <sourceFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/alt_sld_fab_wrapper_hw.tcl" />
  </sourceFiles>
  <childSourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_instrumentation_fabric/alt_sld_fab_hw.tcl" />
   <file
       path="D:/Altera_fpga/ip/altera/sld/core/altera_instrumentation_fabric/com.altera.superfabric.jar" />
   <file
       path="D:/Altera_fpga/quartus/sopc_builder/model/lib/com.altera.utilities.jar" />
   <file
       path="D:/Altera_fpga/quartus/sopc_builder/model/lib/hamcrest-all-1.3.jar" />
   <file
       path="D:/Altera_fpga/quartus/sopc_builder/model/lib/commons-lang3-3.1.jar" />
   <file path="D:/Altera_fpga/quartus/sopc_builder/model/lib/javasysmon.jar" />
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_super_splitter/altera_super_splitter_hw.tcl" />
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_super_splitter/altera_sld_splitter_hw.tcl" />
   <file
       path="D:/altera_fpga/ip/altera/sld/jtag/altera_sld_jtag_hub/altera_sld_jtag_hub_hw.tcl" />
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_connection_identification_hub/altera_connection_identification_hub_hw.tcl" />
  </childSourceFiles>
  <messages>
   <message level="Debug" culprit="alt_sld_fab">queue size: 0 starting:alt_sld_fab "alt_sld_fab"</message>
   <message level="Progress" culprit="min"></message>
   <message level="Progress" culprit="max"></message>
   <message level="Progress" culprit="current"></message>
   <message level="Debug">Transform: CustomInstructionTransform</message>
   <message level="Debug">No custom instruction connections, skipping transform </message>
   <message level="Debug" culprit="merlin_custom_instruction_transform"><![CDATA[After transform: <b>1</b> modules, <b>0</b> connections]]></message>
   <message level="Debug">Transform: MMTransform</message>
   <message level="Debug">Transform: InterruptMapperTransform</message>
   <message level="Debug">Transform: InterruptSyncTransform</message>
   <message level="Debug">Transform: InterruptFanoutTransform</message>
   <message level="Debug">Transform: AvalonStreamingTransform</message>
   <message level="Debug">Transform: ResetAdaptation</message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>alt_sld_fab</b> "<b>submodules/alt_sld_fab_alt_sld_fab</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 0 starting:alt_sld_fab "submodules/alt_sld_fab_alt_sld_fab"</message>
   <message level="Progress" culprit="min"></message>
   <message level="Progress" culprit="max"></message>
   <message level="Progress" culprit="current"></message>
   <message level="Debug">Transform: CustomInstructionTransform</message>
   <message level="Debug">No custom instruction connections, skipping transform </message>
   <message level="Debug" culprit="merlin_custom_instruction_transform"><![CDATA[After transform: <b>5</b> modules, <b>6</b> connections]]></message>
   <message level="Debug">Transform: MMTransform</message>
   <message level="Debug">Transform: InterruptMapperTransform</message>
   <message level="Debug">Transform: InterruptSyncTransform</message>
   <message level="Debug">Transform: InterruptFanoutTransform</message>
   <message level="Debug">Transform: AvalonStreamingTransform</message>
   <message level="Debug">Transform: ResetAdaptation</message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_super_splitter</b> "<b>submodules/alt_sld_fab_alt_sld_fab_presplit</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_sld_splitter</b> "<b>submodules/alt_sld_fab_alt_sld_fab_splitter</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_sld_jtag_hub</b> "<b>submodules/alt_sld_fab_alt_sld_fab_sldfabric</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_connection_identification_hub</b> "<b>submodules/alt_sld_fab_alt_sld_fab_ident</b>"]]></message>
   <message level="Info" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>alt_sld_fab</b> "<b>alt_sld_fab</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 3 starting:altera_super_splitter "submodules/alt_sld_fab_alt_sld_fab_presplit"</message>
   <message level="Info" culprit="presplit"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_super_splitter</b> "<b>presplit</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 2 starting:altera_sld_splitter "submodules/alt_sld_fab_alt_sld_fab_splitter"</message>
   <message level="Info" culprit="splitter"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_sld_splitter</b> "<b>splitter</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 1 starting:altera_sld_jtag_hub "submodules/alt_sld_fab_alt_sld_fab_sldfabric"</message>
   <message level="Info" culprit="sldfabric"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_sld_jtag_hub</b> "<b>sldfabric</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 0 starting:altera_connection_identification_hub "submodules/alt_sld_fab_alt_sld_fab_ident"</message>
   <message level="Info" culprit="ident"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_connection_identification_hub</b> "<b>ident</b>"]]></message>
  </messages>
 </entity>
 <entity
   path="submodules/"
   parameterizationKey="alt_sld_fab:18.1:AGENTS=,AUTO_DEVICE=Unknown,AUTO_DEVICE_SPEEDGRADE=Unknown,CLOCKS={id {} } ,COMPOSED_SETTINGS={fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } ,DESIGN_HASH=0c9bddac927be960db37,DEVICE_FAMILY=Cyclone IV E,EP_INFOS={hpath {sld_signaltap:auto_signaltap_0} } ,MAX_WIDTH=33,MIRROR=0,NODE_COUNT=1,SETTINGS={fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 psig 9b67919e} ,TOP_HUB=1(altera_super_splitter:18.1:MAX_WIDTH=33,RECEIVE_WIDTHS=33,SEND_WIDTHS=12)(altera_sld_splitter:18.1:ADD_INTERFACE_ASGN=0,EXAMPLE=,FRAGMENTS={{name clock type clock dir end ports { {tck clk in 1 0} } } {name node type conduit dir end ports { {tms tms in 1 1} {tdi tdi in 1 2} {tdo tdo out 1 0} {ena ena in 1 3} {usr1 usr1 in 1 4} {clr clr in 1 5} {clrn clrn in 1 6} {jtag_state_tlr jtag_state_tlr in 1 7} {jtag_state_rti jtag_state_rti in 1 8} {jtag_state_sdrs jtag_state_sdrs in 1 9} {jtag_state_cdr jtag_state_cdr in 1 10} {jtag_state_sdr jtag_state_sdr in 1 11} {jtag_state_e1dr jtag_state_e1dr in 1 12} {jtag_state_pdr jtag_state_pdr in 1 13} {jtag_state_e2dr jtag_state_e2dr in 1 14} {jtag_state_udr jtag_state_udr in 1 15} {jtag_state_sirs jtag_state_sirs in 1 16} {jtag_state_cir jtag_state_cir in 1 17} {jtag_state_sir jtag_state_sir in 1 18} {jtag_state_e1ir jtag_state_e1ir in 1 19} {jtag_state_pir jtag_state_pir in 1 20} {jtag_state_e2ir jtag_state_e2ir in 1 21} {jtag_state_uir jtag_state_uir in 1 22} {ir_in ir_in in 10 23} {irq irq out 1 1} {ir_out ir_out out 10 2} } clock clock assign {debug.controlledBy {link_0} } moduleassign {debug.virtualInterface.link_0 {debug.endpointLink {fabric sld index 1} } } } } )(altera_jtag_pins_bridge:18.1:)(altera_sld_jtag_hub:18.1:BROADCAST_FEATURE=0,COMPILATION_MODE=0,CONN_INDEX=0,COUNT=1,DEVICE_FAMILY=Cyclone IV E,ENABLE_SOFT_CORE_CONTROLLER=0,FORCE_IR_CAPTURE_FEATURE=1,FORCE_PRE_1_4_FEATURE=0,NEGEDGE_TDO_LATCH=1,NODE_INFO=00110000000000000110111000000000,N_NODE_IR_BITS=10,N_SEL_BITS=1,SETTINGS={mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } ,TOP_HUB=1)(altera_connection_identification_hub:18.1:COUNT=1,DESIGN_HASH=0c9bddac927be960db37,SETTINGS={width 4 latency 0} )(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)(clock:18.1:)(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)(clock:18.1:)(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)(conduit:18.1:endPort=,endPortLSB=0,startPort=,startPortLSB=0,width=0)"
   instancePathKey="alt_sld_fab:.:alt_sld_fab"
   kind="alt_sld_fab"
   version="18.1"
   name="alt_sld_fab_alt_sld_fab">
  <parameter name="MAX_WIDTH" value="33" />
  <parameter name="TOP_HUB" value="1" />
  <parameter name="DEVICE_FAMILY" value="Cyclone IV E" />
  <parameter
     name="SETTINGS"
     value="{fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 psig 9b67919e} " />
  <parameter name="CLOCKS" value="{id {} } " />
  <parameter name="DESIGN_HASH" value="0c9bddac927be960db37" />
  <parameter name="AUTO_DEVICE_SPEEDGRADE" value="Unknown" />
  <parameter name="MIRROR" value="0" />
  <parameter
     name="COMPOSED_SETTINGS"
     value="{fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } " />
  <parameter name="AUTO_DEVICE" value="Unknown" />
  <parameter name="EP_INFOS" value="{hpath {sld_signaltap:auto_signaltap_0} } " />
  <parameter name="AGENTS" value="" />
  <parameter name="NODE_COUNT" value="1" />
  <generatedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab.v"
       type="VERILOG" />
  </generatedFiles>
  <childGeneratedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_presplit.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_splitter.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd"
       type="VHDL"
       attributes="" />
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_ident.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
  </childGeneratedFiles>
  <sourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_instrumentation_fabric/alt_sld_fab_hw.tcl" />
   <file
       path="D:/Altera_fpga/ip/altera/sld/core/altera_instrumentation_fabric/com.altera.superfabric.jar" />
   <file
       path="D:/Altera_fpga/quartus/sopc_builder/model/lib/com.altera.utilities.jar" />
   <file
       path="D:/Altera_fpga/quartus/sopc_builder/model/lib/hamcrest-all-1.3.jar" />
   <file
       path="D:/Altera_fpga/quartus/sopc_builder/model/lib/commons-lang3-3.1.jar" />
   <file path="D:/Altera_fpga/quartus/sopc_builder/model/lib/javasysmon.jar" />
  </sourceFiles>
  <childSourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_super_splitter/altera_super_splitter_hw.tcl" />
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_super_splitter/altera_sld_splitter_hw.tcl" />
   <file
       path="D:/altera_fpga/ip/altera/sld/jtag/altera_sld_jtag_hub/altera_sld_jtag_hub_hw.tcl" />
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_connection_identification_hub/altera_connection_identification_hub_hw.tcl" />
  </childSourceFiles>
  <instantiator instantiator="alt_sld_fab" as="alt_sld_fab" />
  <messages>
   <message level="Debug" culprit="alt_sld_fab">queue size: 0 starting:alt_sld_fab "submodules/alt_sld_fab_alt_sld_fab"</message>
   <message level="Progress" culprit="min"></message>
   <message level="Progress" culprit="max"></message>
   <message level="Progress" culprit="current"></message>
   <message level="Debug">Transform: CustomInstructionTransform</message>
   <message level="Debug">No custom instruction connections, skipping transform </message>
   <message level="Debug" culprit="merlin_custom_instruction_transform"><![CDATA[After transform: <b>5</b> modules, <b>6</b> connections]]></message>
   <message level="Debug">Transform: MMTransform</message>
   <message level="Debug">Transform: InterruptMapperTransform</message>
   <message level="Debug">Transform: InterruptSyncTransform</message>
   <message level="Debug">Transform: InterruptFanoutTransform</message>
   <message level="Debug">Transform: AvalonStreamingTransform</message>
   <message level="Debug">Transform: ResetAdaptation</message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_super_splitter</b> "<b>submodules/alt_sld_fab_alt_sld_fab_presplit</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_sld_splitter</b> "<b>submodules/alt_sld_fab_alt_sld_fab_splitter</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_sld_jtag_hub</b> "<b>submodules/alt_sld_fab_alt_sld_fab_sldfabric</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" reuses <b>altera_connection_identification_hub</b> "<b>submodules/alt_sld_fab_alt_sld_fab_ident</b>"]]></message>
   <message level="Info" culprit="alt_sld_fab"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>alt_sld_fab</b> "<b>alt_sld_fab</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 3 starting:altera_super_splitter "submodules/alt_sld_fab_alt_sld_fab_presplit"</message>
   <message level="Info" culprit="presplit"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_super_splitter</b> "<b>presplit</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 2 starting:altera_sld_splitter "submodules/alt_sld_fab_alt_sld_fab_splitter"</message>
   <message level="Info" culprit="splitter"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_sld_splitter</b> "<b>splitter</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 1 starting:altera_sld_jtag_hub "submodules/alt_sld_fab_alt_sld_fab_sldfabric"</message>
   <message level="Info" culprit="sldfabric"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_sld_jtag_hub</b> "<b>sldfabric</b>"]]></message>
   <message level="Debug" culprit="alt_sld_fab">queue size: 0 starting:altera_connection_identification_hub "submodules/alt_sld_fab_alt_sld_fab_ident"</message>
   <message level="Info" culprit="ident"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_connection_identification_hub</b> "<b>ident</b>"]]></message>
  </messages>
 </entity>
 <entity
   path="submodules/"
   parameterizationKey="altera_super_splitter:18.1:MAX_WIDTH=33,RECEIVE_WIDTHS=33,SEND_WIDTHS=12"
   instancePathKey="alt_sld_fab:.:alt_sld_fab:.:presplit"
   kind="altera_super_splitter"
   version="18.1"
   name="alt_sld_fab_alt_sld_fab_presplit">
  <parameter name="SEND_WIDTHS" value="12" />
  <parameter name="MAX_WIDTH" value="33" />
  <parameter name="RECEIVE_WIDTHS" value="33" />
  <generatedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_presplit.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
  </generatedFiles>
  <childGeneratedFiles/>
  <sourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_super_splitter/altera_super_splitter_hw.tcl" />
  </sourceFiles>
  <childSourceFiles/>
  <instantiator instantiator="alt_sld_fab_alt_sld_fab" as="presplit" />
  <messages>
   <message level="Debug" culprit="alt_sld_fab">queue size: 3 starting:altera_super_splitter "submodules/alt_sld_fab_alt_sld_fab_presplit"</message>
   <message level="Info" culprit="presplit"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_super_splitter</b> "<b>presplit</b>"]]></message>
  </messages>
 </entity>
 <entity
   path="submodules/"
   parameterizationKey="altera_sld_splitter:18.1:ADD_INTERFACE_ASGN=0,EXAMPLE=,FRAGMENTS={{name clock type clock dir end ports { {tck clk in 1 0} } } {name node type conduit dir end ports { {tms tms in 1 1} {tdi tdi in 1 2} {tdo tdo out 1 0} {ena ena in 1 3} {usr1 usr1 in 1 4} {clr clr in 1 5} {clrn clrn in 1 6} {jtag_state_tlr jtag_state_tlr in 1 7} {jtag_state_rti jtag_state_rti in 1 8} {jtag_state_sdrs jtag_state_sdrs in 1 9} {jtag_state_cdr jtag_state_cdr in 1 10} {jtag_state_sdr jtag_state_sdr in 1 11} {jtag_state_e1dr jtag_state_e1dr in 1 12} {jtag_state_pdr jtag_state_pdr in 1 13} {jtag_state_e2dr jtag_state_e2dr in 1 14} {jtag_state_udr jtag_state_udr in 1 15} {jtag_state_sirs jtag_state_sirs in 1 16} {jtag_state_cir jtag_state_cir in 1 17} {jtag_state_sir jtag_state_sir in 1 18} {jtag_state_e1ir jtag_state_e1ir in 1 19} {jtag_state_pir jtag_state_pir in 1 20} {jtag_state_e2ir jtag_state_e2ir in 1 21} {jtag_state_uir jtag_state_uir in 1 22} {ir_in ir_in in 10 23} {irq irq out 1 1} {ir_out ir_out out 10 2} } clock clock assign {debug.controlledBy {link_0} } moduleassign {debug.virtualInterface.link_0 {debug.endpointLink {fabric sld index 1} } } } } "
   instancePathKey="alt_sld_fab:.:alt_sld_fab:.:splitter"
   kind="altera_sld_splitter"
   version="18.1"
   name="alt_sld_fab_alt_sld_fab_splitter">
  <parameter
     name="FRAGMENTS"
     value="{{name clock type clock dir end ports { {tck clk in 1 0} } } {name node type conduit dir end ports { {tms tms in 1 1} {tdi tdi in 1 2} {tdo tdo out 1 0} {ena ena in 1 3} {usr1 usr1 in 1 4} {clr clr in 1 5} {clrn clrn in 1 6} {jtag_state_tlr jtag_state_tlr in 1 7} {jtag_state_rti jtag_state_rti in 1 8} {jtag_state_sdrs jtag_state_sdrs in 1 9} {jtag_state_cdr jtag_state_cdr in 1 10} {jtag_state_sdr jtag_state_sdr in 1 11} {jtag_state_e1dr jtag_state_e1dr in 1 12} {jtag_state_pdr jtag_state_pdr in 1 13} {jtag_state_e2dr jtag_state_e2dr in 1 14} {jtag_state_udr jtag_state_udr in 1 15} {jtag_state_sirs jtag_state_sirs in 1 16} {jtag_state_cir jtag_state_cir in 1 17} {jtag_state_sir jtag_state_sir in 1 18} {jtag_state_e1ir jtag_state_e1ir in 1 19} {jtag_state_pir jtag_state_pir in 1 20} {jtag_state_e2ir jtag_state_e2ir in 1 21} {jtag_state_uir jtag_state_uir in 1 22} {ir_in ir_in in 10 23} {irq irq out 1 1} {ir_out ir_out out 10 2} } clock clock assign {debug.controlledBy {link_0} } moduleassign {debug.virtualInterface.link_0 {debug.endpointLink {fabric sld index 1} } } } } " />
  <parameter name="EXAMPLE" value="" />
  <parameter name="ADD_INTERFACE_ASGN" value="0" />
  <generatedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_splitter.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
  </generatedFiles>
  <childGeneratedFiles/>
  <sourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_super_splitter/altera_sld_splitter_hw.tcl" />
  </sourceFiles>
  <childSourceFiles/>
  <instantiator instantiator="alt_sld_fab_alt_sld_fab" as="splitter" />
  <messages>
   <message level="Debug" culprit="alt_sld_fab">queue size: 2 starting:altera_sld_splitter "submodules/alt_sld_fab_alt_sld_fab_splitter"</message>
   <message level="Info" culprit="splitter"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_sld_splitter</b> "<b>splitter</b>"]]></message>
  </messages>
 </entity>
 <entity
   path="submodules/"
   parameterizationKey="altera_sld_jtag_hub:18.1:BROADCAST_FEATURE=0,COMPILATION_MODE=0,CONN_INDEX=0,COUNT=1,DEVICE_FAMILY=Cyclone IV E,ENABLE_SOFT_CORE_CONTROLLER=0,FORCE_IR_CAPTURE_FEATURE=1,FORCE_PRE_1_4_FEATURE=0,NEGEDGE_TDO_LATCH=1,NODE_INFO=00110000000000000110111000000000,N_NODE_IR_BITS=10,N_SEL_BITS=1,SETTINGS={mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } ,TOP_HUB=1"
   instancePathKey="alt_sld_fab:.:alt_sld_fab:.:sldfabric"
   kind="altera_sld_jtag_hub"
   version="18.1"
   name="alt_sld_fab_alt_sld_fab_sldfabric">
  <parameter name="CONN_INDEX" value="0" />
  <parameter name="N_SEL_BITS" value="1" />
  <parameter name="TOP_HUB" value="1" />
  <parameter name="DEVICE_FAMILY" value="Cyclone IV E" />
  <parameter name="N_NODE_IR_BITS" value="10" />
  <parameter
     name="SETTINGS"
     value="{mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } " />
  <parameter name="FORCE_IR_CAPTURE_FEATURE" value="1" />
  <parameter name="BROADCAST_FEATURE" value="0" />
  <parameter name="NODE_INFO" value="00110000000000000110111000000000" />
  <parameter name="COMPILATION_MODE" value="0" />
  <parameter name="ENABLE_SOFT_CORE_CONTROLLER" value="0" />
  <parameter name="NEGEDGE_TDO_LATCH" value="1" />
  <parameter name="FORCE_PRE_1_4_FEATURE" value="0" />
  <parameter name="COUNT" value="1" />
  <generatedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd"
       type="VHDL"
       attributes="" />
  </generatedFiles>
  <childGeneratedFiles/>
  <sourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/jtag/altera_sld_jtag_hub/altera_sld_jtag_hub_hw.tcl" />
  </sourceFiles>
  <childSourceFiles/>
  <instantiator instantiator="alt_sld_fab_alt_sld_fab" as="sldfabric" />
  <messages>
   <message level="Debug" culprit="alt_sld_fab">queue size: 1 starting:altera_sld_jtag_hub "submodules/alt_sld_fab_alt_sld_fab_sldfabric"</message>
   <message level="Info" culprit="sldfabric"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_sld_jtag_hub</b> "<b>sldfabric</b>"]]></message>
  </messages>
 </entity>
 <entity
   path="submodules/"
   parameterizationKey="altera_connection_identification_hub:18.1:COUNT=1,DESIGN_HASH=0c9bddac927be960db37,SETTINGS={width 4 latency 0} "
   instancePathKey="alt_sld_fab:.:alt_sld_fab:.:ident"
   kind="altera_connection_identification_hub"
   version="18.1"
   name="alt_sld_fab_alt_sld_fab_ident">
  <parameter name="COUNT" value="1" />
  <parameter name="SETTINGS" value="{width 4 latency 0} " />
  <parameter name="DESIGN_HASH" value="0c9bddac927be960db37" />
  <generatedFiles>
   <file
       path="D:/Altera_fpga/project/zuolan_FPGA/prj/db/ip/sld1184a202/submodules/alt_sld_fab_alt_sld_fab_ident.sv"
       type="SYSTEM_VERILOG"
       attributes="TOP_LEVEL_FILE" />
  </generatedFiles>
  <childGeneratedFiles/>
  <sourceFiles>
   <file
       path="D:/altera_fpga/ip/altera/sld/core/altera_connection_identification_hub/altera_connection_identification_hub_hw.tcl" />
  </sourceFiles>
  <childSourceFiles/>
  <instantiator instantiator="alt_sld_fab_alt_sld_fab" as="ident" />
  <messages>
   <message level="Debug" culprit="alt_sld_fab">queue size: 0 starting:altera_connection_identification_hub "submodules/alt_sld_fab_alt_sld_fab_ident"</message>
   <message level="Info" culprit="ident"><![CDATA["<b>alt_sld_fab</b>" instantiated <b>altera_connection_identification_hub</b> "<b>ident</b>"]]></message>
  </messages>
 </entity>
</deploy>
