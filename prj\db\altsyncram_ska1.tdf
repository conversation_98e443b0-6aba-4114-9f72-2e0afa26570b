--altsyncram ACF_BLOCK_RAM_AND_MLAB_EQUIVALENT_PAUSED_READ_CAPABILITIES="CARE" ADDRESS_REG_B="CLOCK1" CLOCK_ENABLE_INPUT_B="BYPASS" CYCLONEII_M4K_COMPATIBILITY="ON" DEVICE_FAMILY="Cyclone V" ENABLE_ECC="FALSE" LOW_POWER_MODE="AUTO" OPERATION_MODE="DUAL_PORT" OUTDATA_ACLR_B="CLEAR1" OUTDATA_REG_B="CLOCK1" WIDTH_A=12 WIDTH_B=12 WIDTH_BYTEENA_A=1 WIDTH_ECCSTATUS=2 WIDTHAD_A=10 WIDTHAD_B=10 address_a address_b addressstall_b clock0 clock1 clocken1 data_a q_b wren_a
--VERSION_BEGIN 18.1 cbx_altera_syncram_nd_impl 2018:09:12:13:04:24:SJ cbx_altsyncram 2018:09:12:13:04:24:SJ cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_compare 2018:09:12:13:04:24:SJ cbx_lpm_decode 2018:09:12:13:04:24:SJ cbx_lpm_mux 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ cbx_stratixiii 2018:09:12:13:04:24:SJ cbx_stratixv 2018:09:12:13:04:24:SJ cbx_util_mgl 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION cyclonev_ram_block (clk0, clk1, clr0, clr1, ena0, ena1, ena2, ena3, portaaddr[PORT_A_ADDRESS_WIDTH-1..0], portaaddrstall, portabyteenamasks[PORT_A_BYTE_ENABLE_MASK_WIDTH-1..0], portadatain[PORT_A_DATA_WIDTH-1..0], portare, portawe, portbaddr[PORT_B_ADDRESS_WIDTH-1..0], portbaddrstall, portbbyteenamasks[PORT_B_BYTE_ENABLE_MASK_WIDTH-1..0], portbdatain[PORT_B_DATA_WIDTH-1..0], portbre, portbwe)
WITH ( CLK0_CORE_CLOCK_ENABLE, CLK0_INPUT_CLOCK_ENABLE, CLK0_OUTPUT_CLOCK_ENABLE, CLK1_CORE_CLOCK_ENABLE, CLK1_INPUT_CLOCK_ENABLE, CLK1_OUTPUT_CLOCK_ENABLE, CONNECTIVITY_CHECKING, DATA_INTERLEAVE_OFFSET_IN_BITS, DATA_INTERLEAVE_WIDTH_IN_BITS, DONT_POWER_OPTIMIZE, ENABLE_ECC, INIT_FILE, INIT_FILE_LAYOUT, LOGICAL_RAM_NAME, mem_init0, mem_init1, mem_init10, mem_init11, mem_init12, mem_init13, mem_init14, mem_init15, mem_init16, mem_init17, mem_init18, mem_init19, mem_init2, mem_init20, mem_init21, mem_init22, mem_init23, mem_init24, mem_init25, mem_init26, mem_init27, mem_init28, mem_init29, mem_init3, mem_init30, mem_init31, mem_init32, mem_init33, mem_init34, mem_init35, mem_init36, mem_init37, mem_init38, mem_init39, mem_init4, mem_init40, mem_init41, mem_init42, mem_init43, mem_init44, mem_init45, mem_init46, mem_init47, mem_init48, mem_init49, mem_init5, mem_init50, mem_init51, mem_init52, mem_init53, mem_init54, mem_init55, mem_init56, mem_init57, mem_init58, mem_init59, mem_init6, mem_init60, mem_init61, mem_init62, mem_init63, mem_init64, mem_init65, mem_init66, mem_init67, mem_init68, mem_init69, mem_init7, mem_init70, mem_init71, mem_init8, mem_init9, MIXED_PORT_FEED_THROUGH_MODE, OPERATION_MODE, PORT_A_ADDRESS_CLEAR, PORT_A_ADDRESS_WIDTH = 1, PORT_A_BYTE_ENABLE_MASK_WIDTH = 1, PORT_A_BYTE_SIZE, PORT_A_DATA_OUT_CLEAR, PORT_A_DATA_OUT_CLOCK, PORT_A_DATA_WIDTH = 1, PORT_A_FIRST_ADDRESS, PORT_A_FIRST_BIT_NUMBER, PORT_A_LAST_ADDRESS, PORT_A_LOGICAL_RAM_DEPTH, PORT_A_LOGICAL_RAM_WIDTH, PORT_A_READ_DURING_WRITE_MODE, PORT_B_ADDRESS_CLEAR, PORT_B_ADDRESS_CLOCK, PORT_B_ADDRESS_WIDTH = 1, PORT_B_BYTE_ENABLE_CLOCK, PORT_B_BYTE_ENABLE_MASK_WIDTH = 1, PORT_B_BYTE_SIZE, PORT_B_DATA_IN_CLOCK, PORT_B_DATA_OUT_CLEAR, PORT_B_DATA_OUT_CLOCK, PORT_B_DATA_WIDTH = 1, PORT_B_FIRST_ADDRESS, PORT_B_FIRST_BIT_NUMBER, PORT_B_LAST_ADDRESS, PORT_B_LOGICAL_RAM_DEPTH, PORT_B_LOGICAL_RAM_WIDTH, PORT_B_READ_DURING_WRITE_MODE, PORT_B_READ_ENABLE_CLOCK, PORT_B_WRITE_ENABLE_CLOCK, POWER_UP_UNINITIALIZED, RAM_BLOCK_TYPE, WIDTH_ECCSTATUS = 3)
RETURNS ( dftout[8..0], eccstatus[WIDTH_ECCSTATUS-1..0], portadataout[PORT_A_DATA_WIDTH-1..0], portbdataout[PORT_B_DATA_WIDTH-1..0]);

--synthesis_resources = M10K 2 
OPTIONS ALTERA_INTERNAL_OPTION = "OPTIMIZE_POWER_DURING_SYNTHESIS=NORMAL_COMPILATION";

SUBDESIGN altsyncram_ska1
( 
	address_a[9..0]	:	input;
	address_b[9..0]	:	input;
	addressstall_b	:	input;
	clock0	:	input;
	clock1	:	input;
	clocken1	:	input;
	data_a[11..0]	:	input;
	q_b[11..0]	:	output;
	wren_a	:	input;
) 
VARIABLE 
	ram_block11a0 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 0,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 0,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a1 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 1,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 1,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a2 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 2,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 2,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a3 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 3,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 3,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a4 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 4,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 4,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a5 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 5,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 5,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a6 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 6,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 6,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a7 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 7,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 7,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a8 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 8,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 8,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a9 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 9,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 9,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a10 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 10,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 10,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block11a11 : cyclonev_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "none",
			CLK1_INPUT_CLOCK_ENABLE = "none",
			CLK1_OUTPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 10,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 11,
			PORT_A_LAST_ADDRESS = 1023,
			PORT_A_LOGICAL_RAM_DEPTH = 1024,
			PORT_A_LOGICAL_RAM_WIDTH = 12,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 10,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_OUT_CLOCK = "clock1",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 11,
			PORT_B_LAST_ADDRESS = 1023,
			PORT_B_LOGICAL_RAM_DEPTH = 1024,
			PORT_B_LOGICAL_RAM_WIDTH = 12,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			RAM_BLOCK_TYPE = "AUTO"
		);
	address_a_wire[9..0]	: WIRE;
	address_b_wire[9..0]	: WIRE;

BEGIN 
	ram_block11a[11..0].clk0 = clock0;
	ram_block11a[11..0].clk1 = clock1;
	ram_block11a[11..0].ena0 = wren_a;
	ram_block11a[11..0].ena1 = clocken1;
	ram_block11a[11..0].portaaddr[] = ( address_a_wire[9..0]);
	ram_block11a[0].portadatain[] = ( data_a[0..0]);
	ram_block11a[1].portadatain[] = ( data_a[1..1]);
	ram_block11a[2].portadatain[] = ( data_a[2..2]);
	ram_block11a[3].portadatain[] = ( data_a[3..3]);
	ram_block11a[4].portadatain[] = ( data_a[4..4]);
	ram_block11a[5].portadatain[] = ( data_a[5..5]);
	ram_block11a[6].portadatain[] = ( data_a[6..6]);
	ram_block11a[7].portadatain[] = ( data_a[7..7]);
	ram_block11a[8].portadatain[] = ( data_a[8..8]);
	ram_block11a[9].portadatain[] = ( data_a[9..9]);
	ram_block11a[10].portadatain[] = ( data_a[10..10]);
	ram_block11a[11].portadatain[] = ( data_a[11..11]);
	ram_block11a[11..0].portawe = wren_a;
	ram_block11a[11..0].portbaddr[] = ( address_b_wire[9..0]);
	ram_block11a[11..0].portbaddrstall = addressstall_b;
	ram_block11a[11..0].portbre = B"111111111111";
	address_a_wire[] = address_a[];
	address_b_wire[] = address_b[];
	q_b[] = ( ram_block11a[11..0].portbdataout[0..0]);
END;
--VALID FILE
