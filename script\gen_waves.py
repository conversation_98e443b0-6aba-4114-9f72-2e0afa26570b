#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import math

def generate_mif_file(waveform_type, num_points=64, bit_depth=14):
    max_val = (2 ** bit_depth) - 1
    data = []
    
    if waveform_type == 'sine':
        amplitude = max_val / 2
        offset = max_val / 2
        for i in range(num_points):
            value = amplitude * math.sin(2 * math.pi * i / num_points) + offset
            data.append(int(round(value)))
            
    elif waveform_type == 'square':
        half_point = num_points // 2
        for i in range(num_points):
            if i < half_point:
                data.append(max_val)
            else:
                data.append(0)
                
    elif waveform_type == 'triangle':
        half_point = num_points // 2
        for i in range(num_points):
            if i < half_point:
                value = (max_val * i) // half_point
            else:
                value = max_val - ((max_val * (i - half_point)) // (num_points - half_point))
            data.append(int(value))
            
    elif waveform_type == 'sawtooth':
        for i in range(num_points):
            value = (max_val * i) // (num_points - 1)
            data.append(int(value))
    
    # Ensure data is within valid range
    for i in range(len(data)):
        if data[i] > max_val:
            data[i] = max_val
        elif data[i] < 0:
            data[i] = 0
    
    # Generate MIF file
    filename = f"{waveform_type}_64x14.mif"
    
    with open(filename, 'w') as f:
        f.write("-- MIF file generated by Python script\n")
        f.write(f"-- Waveform: {waveform_type}\n")
        f.write("--\n")
        f.write(f"WIDTH = {bit_depth};\n")
        f.write(f"DEPTH = {num_points};\n")
        f.write("ADDRESS_RADIX = DEC;\n")
        f.write("DATA_RADIX = DEC;\n\n")
        f.write("CONTENT BEGIN\n")
        
        for i, value in enumerate(data):
            f.write(f"\t{i} : {value};\n")
        
        f.write("END;\n")
    
    print(f"Generated {filename} with {num_points} points")
    return filename

def main():
    waveforms = ['sine', 'square', 'triangle', 'sawtooth']
    
    print("Generating 64-point waveform MIF files...")
    
    for waveform in waveforms:
        try:
            filename = generate_mif_file(waveform)
            print(f"Success: {filename}")
        except Exception as e:
            print(f"Error generating {waveform}: {e}")
    
    print("All waveform files generated!")

if __name__ == "__main__":
    main()
