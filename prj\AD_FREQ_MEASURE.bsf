/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 384 192)
	(text "AD_FREQ_MEASURE" (rect 5 0 112 12)(font "Arial" ))
	(text "inst" (rect 8 160 20 172)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CS" (rect 0 0 12 12)(font "Arial" ))
		(text "CS" (rect 21 27 33 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "RD" (rect 0 0 15 12)(font "Arial" ))
		(text "RD" (rect 21 43 36 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 1))
	)
	(port
		(pt 0 64)
		(input)
		(text "AD1_FREQ_DATA[31..0]" (rect 0 0 112 12)(font "Arial" ))
		(text "AD1_FREQ_DATA[31..0]" (rect 21 59 133 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 0 80)
		(input)
		(text "AD2_FREQ_DATA[31..0]" (rect 0 0 113 12)(font "Arial" ))
		(text "AD2_FREQ_DATA[31..0]" (rect 21 75 134 87)(font "Arial" ))
		(line (pt 0 80)(pt 16 80)(line_width 3))
	)
	(port
		(pt 0 96)
		(input)
		(text "BASE1_FREQ_DATA[31..0]" (rect 0 0 123 12)(font "Arial" ))
		(text "BASE1_FREQ_DATA[31..0]" (rect 21 91 144 103)(font "Arial" ))
		(line (pt 0 96)(pt 16 96)(line_width 3))
	)
	(port
		(pt 0 112)
		(input)
		(text "BASE2_FREQ_DATA[31..0]" (rect 0 0 125 12)(font "Arial" ))
		(text "BASE2_FREQ_DATA[31..0]" (rect 21 107 146 119)(font "Arial" ))
		(line (pt 0 112)(pt 16 112)(line_width 3))
	)
	(port
		(pt 0 128)
		(input)
		(text "ADDR[15..0]" (rect 0 0 54 12)(font "Arial" ))
		(text "ADDR[15..0]" (rect 21 123 75 135)(font "Arial" ))
		(line (pt 0 128)(pt 16 128)(line_width 3))
	)
	(port
		(pt 368 32)
		(output)
		(text "AD1_FREQ_DATA_H[15..0]" (rect 0 0 125 12)(font "Arial" ))
		(text "AD1_FREQ_DATA_H[15..0]" (rect 222 27 347 39)(font "Arial" ))
		(line (pt 368 32)(pt 352 32)(line_width 3))
	)
	(port
		(pt 368 48)
		(output)
		(text "AD1_FREQ_DATA_L[15..0]" (rect 0 0 123 12)(font "Arial" ))
		(text "AD1_FREQ_DATA_L[15..0]" (rect 224 43 347 55)(font "Arial" ))
		(line (pt 368 48)(pt 352 48)(line_width 3))
	)
	(port
		(pt 368 64)
		(output)
		(text "AD2_FREQ_DATA_H[15..0]" (rect 0 0 126 12)(font "Arial" ))
		(text "AD2_FREQ_DATA_H[15..0]" (rect 221 59 347 71)(font "Arial" ))
		(line (pt 368 64)(pt 352 64)(line_width 3))
	)
	(port
		(pt 368 80)
		(output)
		(text "AD2_FREQ_DATA_L[15..0]" (rect 0 0 125 12)(font "Arial" ))
		(text "AD2_FREQ_DATA_L[15..0]" (rect 222 75 347 87)(font "Arial" ))
		(line (pt 368 80)(pt 352 80)(line_width 3))
	)
	(port
		(pt 368 96)
		(output)
		(text "BASE1_FREQ_DATA_H[15..0]" (rect 0 0 136 12)(font "Arial" ))
		(text "BASE1_FREQ_DATA_H[15..0]" (rect 211 91 347 103)(font "Arial" ))
		(line (pt 368 96)(pt 352 96)(line_width 3))
	)
	(port
		(pt 368 112)
		(output)
		(text "BASE1_FREQ_DATA_L[15..0]" (rect 0 0 135 12)(font "Arial" ))
		(text "BASE1_FREQ_DATA_L[15..0]" (rect 212 107 347 119)(font "Arial" ))
		(line (pt 368 112)(pt 352 112)(line_width 3))
	)
	(port
		(pt 368 128)
		(output)
		(text "BASE2_FREQ_DATA_H[15..0]" (rect 0 0 138 12)(font "Arial" ))
		(text "BASE2_FREQ_DATA_H[15..0]" (rect 209 123 347 135)(font "Arial" ))
		(line (pt 368 128)(pt 352 128)(line_width 3))
	)
	(port
		(pt 368 144)
		(output)
		(text "BASE2_FREQ_DATA_L[15..0]" (rect 0 0 136 12)(font "Arial" ))
		(text "BASE2_FREQ_DATA_L[15..0]" (rect 211 139 347 151)(font "Arial" ))
		(line (pt 368 144)(pt 352 144)(line_width 3))
	)
	(parameter
		"ADDR2"
		"0000000000000010"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR3"
		"0000000000000011"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR4"
		"0000000000000100"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR5"
		"0000000000000101"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR10"
		"0000000000001010"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR11"
		"0000000000001011"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR12"
		"0000000000001100"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR13"
		"0000000000001101"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(drawing
		(rectangle (rect 16 16 352 160)(line_width 1))
	)
	(annotation_block (parameter)(rect 384 -64 484 16))
)
