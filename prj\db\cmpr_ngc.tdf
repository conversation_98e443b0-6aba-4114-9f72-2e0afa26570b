--lpm_compare CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" DEVICE_FAMILY="Cyclone IV E" LPM_WIDTH=1 ONE_INPUT_IS_CONSTANT="YES" aeb dataa datab
--VERSION_BEGIN 18.1 cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_compare 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.



--synthesis_resources = 
SUBDESIGN cmpr_ngc
( 
	aeb	:	output;
	dataa[0..0]	:	input;
	datab[0..0]	:	input;
) 
VARIABLE 
	aeb_result_wire[0..0]	: WIRE;
	aneb_result_wire[0..0]	: WIRE;
	data_wire[1..0]	: WIRE;
	eq_wire	: WIRE;

BEGIN 
	aeb = eq_wire;
	aeb_result_wire[] = (! aneb_result_wire[]);
	aneb_result_wire[] = (data_wire[0..0] $ data_wire[1..1]);
	data_wire[] = ( datab[0..0], dataa[0..0]);
	eq_wire = aeb_result_wire[];
END;
--VALID FILE
