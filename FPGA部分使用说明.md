---
author: 左岚、陆虎
---

# EP4CE10F17 FPGA信号处理系统 - 项目使用说明

## 项目概述

本项目基于EP4CE10F17 FPGA，实现了一个综合的信号处理系统，主要用于波形生成、AD数据处理、频率测量、FMC通信等功能，与STM32F429构成双核信号处理平台。

## 硬件平台

- **FPGA**: EP4CE10F17 (LQFP256封装)
- **系统时钟**: 基于25MHz晶振，内部PLL倍频
- **主要外设**:
  - FMC通信接口
  - 双路AD9226高速ADC
  - AD9764高速DAC
  - 时钟分配网络

## 项目目录结构

```
├── ip/                       # IP核文件
│   ├── MYPLL/               # 时钟PLL
│   ├── sawtooth_rom/        # 锯齿波ROM
│   ├── sinromvpp/           # 正弦波ROM  
│   ├── sqaure_rom/          # 方波ROM
│   ├── triangle_rom/        # 三角波ROM
│   └── TYFIFO/              # FIFO缓冲器
├── prj/                      # 项目文件
│   ├── ZUOLAN_FPGA_OBJECT.qpf  # Quartus项目文件
│   ├── ZUOLAN_FPGA_OBJECT.qsf  # Quartus设置文件
│   ├── TOP.bdf              # 顶层设计文件
│   └── ...                  # 其他项目文件
├── script/                   # 脚本文件
│   ├── ZUOLAN_FPGA_OBJECT.tcl  # TCL脚本
│   ├── sin_1024x14.mif      # 正弦波存储初始化文件
│   ├── square_1024x14.mif   # 方波存储初始化文件
│   ├── triangle_1024x14.mif # 三角波存储初始化文件
│   └── sawtooth_1024x14.mif # 锯齿波存储初始化文件
├── src/                      # 源代码
│   ├── FMC_CONTROL.v        # FMC控制器
│   ├── DA_WAVEFORM_A.v      # DA波形生成A
│   ├── DA_WAVEFORM_B.v      # DA波形生成B
│   ├── AD_DATA_DEAL.v       # AD数据处理
│   ├── AD_FREQ_MEASURE.v    # 频率测量
│   └── ...                  # 其他模块
└── stp/                      # SignalTap文件
    └── stp1.stp             # 逻辑分析文件
```

## 主要功能模块

### 1. FMC通信控制模块 (FMC_CONTROL.v)
- 实现FPGA与STM32之间的高速通信
- 支持16个读通道和16个写通道
- 异步读写操作
- 地址锁存与数据分离

### 2. DA波形发生器模块 (DA_WAVEFORM_A/B.v)
- 支持四种标准波形：正弦波、方波、三角波、锯齿波
- 1024点查表，14位精度
- 实时波形切换
- 相位可调控制

### 3. AD数据处理模块 (AD_DATA_DEAL.v)
- 双路12位ADC数据处理
- FIFO缓冲管理
- 数据位序转换
- 总线接口适配

### 4. 频率测量模块 (AD_FREQ_MEASURE.v)
- 高精度频率测量
- 等精度频率测量法
- 32位计数器
- 自适应门控时间

### 5. 时钟与相位控制模块
- 系统时钟生成与分配
- 多相位时钟输出
- DA输出时钟控制
- 相位关系管理

## 模块架构图

```
┌─────────────────────────────────────────────────────────┐
│                      TOP.bdf                           │
└───────────────────────────┬─────────────────────────────┘
                            │
    ┌───────────────────────┼───────────────────────┐
    │                       │                       │
┌───▼───────────┐    ┌─────▼─────────┐    ┌────────▼────────┐
│ FMC_CONTROL.v │    │ MASTER_CTRL.v │    │ DA_CLK_CTRL.v   │
└───┬───────────┘    └─────┬─────────┘    └────────┬────────┘
    │                      │                       │
    │     ┌───────────────┼───────────────┐       │
    │     │               │               │       │
┌───▼─────▼───┐    ┌─────▼─────┐    ┌────▼───────▼───┐
│ AD_DATA_DEAL │    │ AD_FREQ   │    │ DA_WAVEFORM_A/B │
└───────────┬──┘    │ MEASURE   │    └────────┬────────┘
            │       └───────────┘             │
            │                                 │
            │       ┌───────────────┐         │
            └───────► 外部ADC接口   ◄─────────┘
                    └───────────────┘
```

## 数据流图

```
┌──────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ STM32F429│    │ FMC总线   │    │ FPGA内部  │    │ 外部接口  │
│          ├───►│ 接口      ├───►│ 模块      ├───►│           │
└──────────┘    └───────────┘    └───────────┘    └───────────┘
     ▲                                                  │
     │                                                  │
     └──────────────────────────────────────────────────┘
                          反馈数据
```

## 模块接口定义

### 1. FMC通信控制模块接口

```verilog
module FMC_CONTROL(
    input fpga_nl_nadv,    // 地址锁存使能，低电平有效
    input fpga_cs_ne1,     // 片选信号，低电平有效  
    input fpga_wr_nwe,     // 写使能信号，低电平有效
    input fpga_rd_noe,     // 读使能信号，低电平有效
    inout [15:0] fpga_db,  // 双向地址/数据总线
    
    // 内部接口信号
    output [7:0] addr,     // 内部地址总线
    output [15:0] wdata,   // 写数据总线
    input [15:0] rdata,    // 读数据总线
    output wr_en,          // 写使能
    output rd_en           // 读使能
);
```

### 2. DA波形发生器模块接口

```verilog
module DA_WAVEFORM_A(
    input clk,             // 系统时钟
    input rst_n,           // 复位信号，低电平有效
    input [9:0] phase_in,  // 相位输入
    input [7:0] addr,      // 地址总线
    input [15:0] wdata,    // 写数据总线
    input wr_en,           // 写使能
    output [13:0] da_out   // DA输出数据
);
```

### 3. AD数据处理模块接口

```verilog
module AD_DATA_DEAL(
    input clk,             // 系统时钟
    input rst_n,           // 复位信号，低电平有效
    input [11:0] ad1_data, // AD1数据输入
    input [11:0] ad2_data, // AD2数据输入
    input ad_clk,          // AD采样时钟
    input [7:0] addr,      // 地址总线
    input rd_en,           // 读使能
    output [15:0] rdata    // 读数据总线
);
```

### 4. 频率测量模块接口

```verilog
module AD_FREQ_MEASURE(
    input clk,             // 系统时钟
    input rst_n,           // 复位信号，低电平有效
    input signal_in,       // 待测信号输入
    input [7:0] addr,      // 地址总线
    input rd_en,           // 读使能
    output [15:0] rdata    // 读数据总线
);
```

## 地址映射表

| 地址  | 功能描述                | 读/写 | 数据格式           |
|-------|------------------------|-------|--------------------|
| 0x00  | 波形选择寄存器A        | 写    | [7:0] 波形类型     |
| 0x01  | 波形选择寄存器B        | 写    | [7:0] 波形类型     |
| 0x02  | 频率控制字寄存器A      | 写    | [15:0] 频率控制字  |
| 0x03  | 频率控制字寄存器B      | 写    | [15:0] 频率控制字  |
| 0x04  | 相位控制寄存器A        | 写    | [15:0] 相位控制字  |
| 0x05  | 相位控制寄存器B        | 写    | [15:0] 相位控制字  |
| 0x06  | AD1 FIFO数据读取       | 读    | [11:0] AD1数据     |
| 0x07  | AD1 FIFO状态标志       | 读    | [0] 空标志 [1] 满标志 |
| 0x08  | AD2 FIFO数据读取       | 读    | [11:0] AD2数据     |
| 0x09  | AD2 FIFO状态标志       | 读    | [0] 空标志 [1] 满标志 |
| 0x0A  | 频率测量结果低16位     | 读    | [15:0] 频率值低位  |
| 0x0B  | 频率测量结果高16位     | 读    | [15:0] 频率值高位  |
| 0x0C  | 系统控制寄存器         | 写    | [0] 复位 [1] 使能  |
| 0x0D  | 系统状态寄存器         | 读    | [0] 就绪 [1] 错误  |

## 编译与下载

### 开发环境要求
- **IDE**: Intel Quartus Prime 18.1或更高版本
- **仿真工具**: ModelSim
- **下载器**: USB Blaster

### 编译步骤
1. 打开Quartus Prime软件
2. 打开项目文件 `prj/ZUOLAN_FPGA_OBJECT.qpf`
3. 执行Analysis & Synthesis (分析与综合)
4. 执行Place & Route (布局与布线)
5. 执行Assembler (生成比特流文件)

### 下载步骤
1. 连接USB Blaster下载器
2. 打开Programmer工具
3. 添加SOF文件 `prj/ZUOLAN_FPGA_OBJECT.sof`
4. 选择正确的JTAG链
5. 点击Start按钮开始下载

## 使用说明

### 1. 系统初始化
系统上电后会自动完成以下初始化：
- PLL锁相环锁定
- 内部寄存器复位
- FMC接口初始化
- 默认波形配置

### 2. FMC通信协议

FPGA与STM32之间通过FMC总线进行通信，通信时序如下：

1. **地址阶段**：
   - STM32拉低`fpga_nl_nadv`信号
   - STM32在`fpga_db`总线上输出地址
   - FPGA锁存地址

2. **数据阶段**：
   - 写操作：STM32拉低`fpga_wr_nwe`，在`fpga_db`上输出数据
   - 读操作：STM32拉低`fpga_rd_noe`，FPGA在`fpga_db`上输出数据

3. **结束阶段**：
   - STM32释放控制信号
   - 总线回到空闲状态

### 3. 波形生成控制

#### 波形类型设置
通过写入波形选择寄存器(0x00/0x01)控制波形类型：
- 0x00：正弦波
- 0x01：方波
- 0x02：三角波
- 0x03：锯齿波

#### 频率控制
通过写入频率控制字寄存器(0x02/0x03)设置输出频率：
- 频率计算公式：Fout = Fclk * 频率控制字 / 2^32
- Fclk为系统时钟频率

#### 相位控制
通过写入相位控制寄存器(0x04/0x05)设置输出相位：
- 相位计算公式：Phase = 相位控制字 * 360 / 65536
- 相位范围：0-359.99度

### 4. 数据采集控制

#### AD数据读取
通过读取AD FIFO数据寄存器(0x06/0x08)获取AD采样数据：
- 数据格式：12位无符号整数
- 采样率：由AD_CLK决定

#### FIFO状态检查
通过读取FIFO状态寄存器(0x07/0x09)检查FIFO状态：
- bit0：空标志，1表示FIFO为空
- bit1：满标志，1表示FIFO已满

### 5. 频率测量控制

通过读取频率测量结果寄存器(0x0A/0x0B)获取频率测量值：
- 低16位寄存器(0x0A)：频率值低16位
- 高16位寄存器(0x0B)：频率值高16位
- 频率计算公式：F = 计数值 * Fclk / 门控时间

## 测试与验证

### 1. 硬件测试

#### FMC通信测试
1. 使用STM32写入测试数据
2. 读回数据并验证一致性
3. 测试不同地址的读写操作

#### 波形生成测试
1. 配置不同波形类型
2. 使用示波器观察输出波形
3. 验证频率和相位控制

#### AD采集测试
1. 输入已知测试信号
2. 读取AD数据并分析
3. 验证数据完整性和准确性

#### 频率测量测试
1. 输入已知频率信号
2. 读取频率测量结果
3. 比较测量值与实际值

### 2. SignalTap逻辑分析
- 使用`stp/stp1.stp`文件进行内部信号分析
- 监控关键信号时序关系
- 验证内部状态机工作状态

### 3. 性能指标
- DA输出速率：最高125MSPS
- AD采样率：最高65MSPS
- 频率测量范围：1Hz ~ 100MHz
- 频率测量精度：±0.1Hz

## 注意事项

1. **时钟配置**: 确保25MHz晶振正常工作
2. **电源要求**: 核心电压1.2V，IO电压3.3V
3. **引脚约束**: 严格遵循引脚分配表
4. **下载模式**: 支持JTAG和AS模式下载

## 扩展功能

### 可选功能模块
- 数字滤波器实现
- 复杂波形生成
- 实时FFT处理
- 多通道同步控制

### 自定义配置
- 修改ROM初始化文件定制波形
- 调整PLL配置改变时钟频率
- 扩展地址空间增加功能模块
- 优化资源使用提高性能

## 技术支持

- 查看源代码注释了解详细实现
- 参考EP4CE10F17数据手册
- 使用SignalTap进行问题定位
- 参考项目总体模块讲解文档

