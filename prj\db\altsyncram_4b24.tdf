--altsyncram ADDRESS_ACLR_A="NONE" ADDRESS_ACLR_B="NONE" ADDRESS_REG_B="CLOCK1" BYTE_SIZE=8 BYTEENA_ACLR_A="NONE" BYTEENA_ACLR_B="NONE" BYTEENA_REG_B="CLOCK1" CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" CLOCK_ENABLE_CORE_A="USE_INPUT_CLKEN" CLOCK_ENABLE_CORE_B="USE_INPUT_CLKEN" CLOCK_ENABLE_INPUT_A="NORMAL" CLOCK_ENABLE_INPUT_B="NORMAL" CLOCK_ENABLE_OUTPUT_A="NORMAL" CLOCK_ENABLE_OUTPUT_B="NORMAL" CYCLONEII_M4K_COMPATIBILITY="ON" DEVICE_FAMILY="Cyclone IV E" ECC_PIPELINE_STAGE_ENABLED="FALSE" ENABLE_ECC="FALSE" IMPLEMENT_IN_LES="OFF" INDATA_ACLR_A="NONE" INDATA_ACLR_B="NONE" INDATA_REG_B="CLOCK1" INIT_FILE_LAYOUT="PORT_A" LOW_POWER_MODE="AUTO" MAXIMUM_DEPTH=0 NUMWORDS_A=2048 NUMWORDS_B=2048 OPERATION_MODE="DUAL_PORT" OUTDATA_ACLR_A="NONE" OUTDATA_ACLR_B="NONE" OUTDATA_REG_A="UNREGISTERED" OUTDATA_REG_B="UNREGISTERED" POWER_UP_UNINITIALIZED="FALSE" RAM_BLOCK_TYPE="AUTO" RDCONTROL_ACLR_B="NONE" RDCONTROL_REG_B="CLOCK1" READ_DURING_WRITE_MODE_MIXED_PORTS="DONT_CARE" read_during_write_mode_port_a="NEW_DATA_NO_NBE_READ" read_during_write_mode_port_b="NEW_DATA_NO_NBE_READ" stratixiv_m144k_allow_dual_clocks="ON" WIDTH_A=54 WIDTH_B=54 WIDTH_BYTEENA_A=1 WIDTH_BYTEENA_B=1 WIDTH_ECCSTATUS=3 WIDTHAD_A=11 WIDTHAD_B=11 WRCONTROL_ACLR_A="NONE" WRCONTROL_ACLR_B="NONE" WRCONTROL_WRADDRESS_REG_B="CLOCK1" address_a address_b clock0 clock1 clocken1 data_a q_b wren_a CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48
--VERSION_BEGIN 18.1 cbx_altera_syncram_nd_impl 2018:09:12:13:04:24:SJ cbx_altsyncram 2018:09:12:13:04:24:SJ cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_compare 2018:09:12:13:04:24:SJ cbx_lpm_decode 2018:09:12:13:04:24:SJ cbx_lpm_mux 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ cbx_stratixiii 2018:09:12:13:04:24:SJ cbx_stratixv 2018:09:12:13:04:24:SJ cbx_util_mgl 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION cycloneive_ram_block (clk0, clk1, clr0, clr1, ena0, ena1, ena2, ena3, portaaddr[PORT_A_ADDRESS_WIDTH-1..0], portaaddrstall, portabyteenamasks[PORT_A_BYTE_ENABLE_MASK_WIDTH-1..0], portadatain[PORT_A_DATA_WIDTH-1..0], portare, portawe, portbaddr[PORT_B_ADDRESS_WIDTH-1..0], portbaddrstall, portbbyteenamasks[PORT_B_BYTE_ENABLE_MASK_WIDTH-1..0], portbdatain[PORT_B_DATA_WIDTH-1..0], portbre, portbwe)
WITH ( CLK0_CORE_CLOCK_ENABLE, CLK0_INPUT_CLOCK_ENABLE, CLK0_OUTPUT_CLOCK_ENABLE, CLK1_CORE_CLOCK_ENABLE, CLK1_INPUT_CLOCK_ENABLE, CLK1_OUTPUT_CLOCK_ENABLE, CONNECTIVITY_CHECKING, DATA_INTERLEAVE_OFFSET_IN_BITS, DATA_INTERLEAVE_WIDTH_IN_BITS, DONT_POWER_OPTIMIZE, INIT_FILE, INIT_FILE_LAYOUT, init_file_restructured, LOGICAL_RAM_NAME, mem_init0, mem_init1, mem_init2, mem_init3, mem_init4, MIXED_PORT_FEED_THROUGH_MODE, OPERATION_MODE, PORT_A_ADDRESS_CLEAR, PORT_A_ADDRESS_WIDTH = 1, PORT_A_BYTE_ENABLE_MASK_WIDTH = 1, PORT_A_BYTE_SIZE, PORT_A_DATA_OUT_CLEAR, PORT_A_DATA_OUT_CLOCK, PORT_A_DATA_WIDTH = 1, PORT_A_FIRST_ADDRESS, PORT_A_FIRST_BIT_NUMBER, PORT_A_LAST_ADDRESS, PORT_A_LOGICAL_RAM_DEPTH, PORT_A_LOGICAL_RAM_WIDTH, PORT_A_READ_DURING_WRITE_MODE, PORT_B_ADDRESS_CLEAR, PORT_B_ADDRESS_CLOCK, PORT_B_ADDRESS_WIDTH = 1, PORT_B_BYTE_ENABLE_CLOCK, PORT_B_BYTE_ENABLE_MASK_WIDTH = 1, PORT_B_BYTE_SIZE, PORT_B_DATA_IN_CLOCK, PORT_B_DATA_OUT_CLEAR, PORT_B_DATA_OUT_CLOCK, PORT_B_DATA_WIDTH = 1, PORT_B_FIRST_ADDRESS, PORT_B_FIRST_BIT_NUMBER, PORT_B_LAST_ADDRESS, PORT_B_LOGICAL_RAM_DEPTH, PORT_B_LOGICAL_RAM_WIDTH, PORT_B_READ_DURING_WRITE_MODE, PORT_B_READ_ENABLE_CLOCK, PORT_B_WRITE_ENABLE_CLOCK, POWER_UP_UNINITIALIZED, RAM_BLOCK_TYPE, SAFE_WRITE, WIDTH_ECCSTATUS)
RETURNS ( portadataout[PORT_A_DATA_WIDTH-1..0], portbdataout[PORT_B_DATA_WIDTH-1..0]);

--synthesis_resources = M9K 14 
OPTIONS ALTERA_INTERNAL_OPTION = "OPTIMIZE_POWER_DURING_SYNTHESIS=NORMAL_COMPILATION";

SUBDESIGN altsyncram_4b24
( 
	address_a[10..0]	:	input;
	address_b[10..0]	:	input;
	clock0	:	input;
	clock1	:	input;
	clocken1	:	input;
	data_a[53..0]	:	input;
	q_b[53..0]	:	output;
	wren_a	:	input;
) 
VARIABLE 
	ram_block1a0 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 0,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 0,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a1 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 1,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 1,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a2 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 2,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 2,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a3 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 3,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 3,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a4 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 4,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 4,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a5 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 5,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 5,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a6 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 6,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 6,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a7 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 7,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 7,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a8 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 8,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 8,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a9 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 9,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 9,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a10 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 10,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 10,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a11 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 11,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 11,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a12 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 12,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 12,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a13 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 13,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 13,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a14 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 14,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 14,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a15 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 15,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 15,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a16 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 16,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 16,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a17 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 17,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 17,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a18 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 18,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 18,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a19 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 19,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 19,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a20 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 20,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 20,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a21 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 21,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 21,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a22 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 22,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 22,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a23 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 23,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 23,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a24 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 24,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 24,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a25 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 25,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 25,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a26 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 26,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 26,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a27 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 27,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 27,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a28 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 28,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 28,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a29 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 29,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 29,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a30 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 30,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 30,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a31 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 31,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 31,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a32 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 32,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 32,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a33 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 33,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 33,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a34 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 34,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 34,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a35 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 35,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 35,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a36 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 36,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 36,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a37 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 37,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 37,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a38 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 38,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 38,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a39 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 39,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 39,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a40 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 40,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 40,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a41 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 41,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 41,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a42 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 42,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 42,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a43 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 43,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 43,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a44 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 44,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 44,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a45 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 45,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 45,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a46 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 46,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 46,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a47 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 47,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 47,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a48 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 48,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 48,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a49 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 49,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 49,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a50 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 50,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 50,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a51 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 51,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 51,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a52 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 52,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 52,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a53 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK1_CORE_CLOCK_ENABLE = "ena1",
			CLK1_INPUT_CLOCK_ENABLE = "ena1",
			CONNECTIVITY_CHECKING = "OFF",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			MIXED_PORT_FEED_THROUGH_MODE = "dont_care",
			OPERATION_MODE = "dual_port",
			PORT_A_ADDRESS_WIDTH = 11,
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 53,
			PORT_A_LAST_ADDRESS = 2047,
			PORT_A_LOGICAL_RAM_DEPTH = 2048,
			PORT_A_LOGICAL_RAM_WIDTH = 54,
			PORT_B_ADDRESS_CLEAR = "none",
			PORT_B_ADDRESS_CLOCK = "clock1",
			PORT_B_ADDRESS_WIDTH = 11,
			PORT_B_DATA_OUT_CLEAR = "none",
			PORT_B_DATA_WIDTH = 1,
			PORT_B_FIRST_ADDRESS = 0,
			PORT_B_FIRST_BIT_NUMBER = 53,
			PORT_B_LAST_ADDRESS = 2047,
			PORT_B_LOGICAL_RAM_DEPTH = 2048,
			PORT_B_LOGICAL_RAM_WIDTH = 54,
			PORT_B_READ_ENABLE_CLOCK = "clock1",
			POWER_UP_UNINITIALIZED = "false",
			RAM_BLOCK_TYPE = "AUTO"
		);
	address_a_wire[10..0]	: WIRE;
	address_b_wire[10..0]	: WIRE;

BEGIN 
	ram_block1a[53..0].clk0 = clock0;
	ram_block1a[53..0].clk1 = clock1;
	ram_block1a[53..0].ena0 = wren_a;
	ram_block1a[53..0].ena1 = clocken1;
	ram_block1a[53..0].portaaddr[] = ( address_a_wire[10..0]);
	ram_block1a[0].portadatain[] = ( data_a[0..0]);
	ram_block1a[1].portadatain[] = ( data_a[1..1]);
	ram_block1a[2].portadatain[] = ( data_a[2..2]);
	ram_block1a[3].portadatain[] = ( data_a[3..3]);
	ram_block1a[4].portadatain[] = ( data_a[4..4]);
	ram_block1a[5].portadatain[] = ( data_a[5..5]);
	ram_block1a[6].portadatain[] = ( data_a[6..6]);
	ram_block1a[7].portadatain[] = ( data_a[7..7]);
	ram_block1a[8].portadatain[] = ( data_a[8..8]);
	ram_block1a[9].portadatain[] = ( data_a[9..9]);
	ram_block1a[10].portadatain[] = ( data_a[10..10]);
	ram_block1a[11].portadatain[] = ( data_a[11..11]);
	ram_block1a[12].portadatain[] = ( data_a[12..12]);
	ram_block1a[13].portadatain[] = ( data_a[13..13]);
	ram_block1a[14].portadatain[] = ( data_a[14..14]);
	ram_block1a[15].portadatain[] = ( data_a[15..15]);
	ram_block1a[16].portadatain[] = ( data_a[16..16]);
	ram_block1a[17].portadatain[] = ( data_a[17..17]);
	ram_block1a[18].portadatain[] = ( data_a[18..18]);
	ram_block1a[19].portadatain[] = ( data_a[19..19]);
	ram_block1a[20].portadatain[] = ( data_a[20..20]);
	ram_block1a[21].portadatain[] = ( data_a[21..21]);
	ram_block1a[22].portadatain[] = ( data_a[22..22]);
	ram_block1a[23].portadatain[] = ( data_a[23..23]);
	ram_block1a[24].portadatain[] = ( data_a[24..24]);
	ram_block1a[25].portadatain[] = ( data_a[25..25]);
	ram_block1a[26].portadatain[] = ( data_a[26..26]);
	ram_block1a[27].portadatain[] = ( data_a[27..27]);
	ram_block1a[28].portadatain[] = ( data_a[28..28]);
	ram_block1a[29].portadatain[] = ( data_a[29..29]);
	ram_block1a[30].portadatain[] = ( data_a[30..30]);
	ram_block1a[31].portadatain[] = ( data_a[31..31]);
	ram_block1a[32].portadatain[] = ( data_a[32..32]);
	ram_block1a[33].portadatain[] = ( data_a[33..33]);
	ram_block1a[34].portadatain[] = ( data_a[34..34]);
	ram_block1a[35].portadatain[] = ( data_a[35..35]);
	ram_block1a[36].portadatain[] = ( data_a[36..36]);
	ram_block1a[37].portadatain[] = ( data_a[37..37]);
	ram_block1a[38].portadatain[] = ( data_a[38..38]);
	ram_block1a[39].portadatain[] = ( data_a[39..39]);
	ram_block1a[40].portadatain[] = ( data_a[40..40]);
	ram_block1a[41].portadatain[] = ( data_a[41..41]);
	ram_block1a[42].portadatain[] = ( data_a[42..42]);
	ram_block1a[43].portadatain[] = ( data_a[43..43]);
	ram_block1a[44].portadatain[] = ( data_a[44..44]);
	ram_block1a[45].portadatain[] = ( data_a[45..45]);
	ram_block1a[46].portadatain[] = ( data_a[46..46]);
	ram_block1a[47].portadatain[] = ( data_a[47..47]);
	ram_block1a[48].portadatain[] = ( data_a[48..48]);
	ram_block1a[49].portadatain[] = ( data_a[49..49]);
	ram_block1a[50].portadatain[] = ( data_a[50..50]);
	ram_block1a[51].portadatain[] = ( data_a[51..51]);
	ram_block1a[52].portadatain[] = ( data_a[52..52]);
	ram_block1a[53].portadatain[] = ( data_a[53..53]);
	ram_block1a[53..0].portawe = wren_a;
	ram_block1a[53..0].portbaddr[] = ( address_b_wire[10..0]);
	ram_block1a[53..0].portbre = B"111111111111111111111111111111111111111111111111111111";
	address_a_wire[] = address_a[];
	address_b_wire[] = address_b[];
	q_b[] = ( ram_block1a[53..0].portbdataout[0..0]);
END;
--VALID FILE
