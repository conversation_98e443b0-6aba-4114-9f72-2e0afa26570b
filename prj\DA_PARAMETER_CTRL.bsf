/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 376 256)
	(text "DA_PARAMETER_CTRL" (rect 5 0 126 12)(font "Arial" ))
	(text "inst" (rect 8 224 20 236)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CLK_BASE" (rect 0 0 53 12)(font "Arial" ))
		(text "CLK_BASE" (rect 21 27 74 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "EN" (rect 0 0 14 12)(font "Arial" ))
		(text "EN" (rect 21 43 35 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 1))
	)
	(port
		(pt 0 64)
		(input)
		(text "FREQAH_W[15..0]" (rect 0 0 83 12)(font "Arial" ))
		(text "FREQAH_W[15..0]" (rect 21 59 104 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 0 80)
		(input)
		(text "FREQAL_W[15..0]" (rect 0 0 82 12)(font "Arial" ))
		(text "FREQAL_W[15..0]" (rect 21 75 103 87)(font "Arial" ))
		(line (pt 0 80)(pt 16 80)(line_width 3))
	)
	(port
		(pt 0 96)
		(input)
		(text "FREQBH_W[15..0]" (rect 0 0 81 12)(font "Arial" ))
		(text "FREQBH_W[15..0]" (rect 21 91 102 103)(font "Arial" ))
		(line (pt 0 96)(pt 16 96)(line_width 3))
	)
	(port
		(pt 0 112)
		(input)
		(text "FREQBL_W[15..0]" (rect 0 0 80 12)(font "Arial" ))
		(text "FREQBL_W[15..0]" (rect 21 107 101 119)(font "Arial" ))
		(line (pt 0 112)(pt 16 112)(line_width 3))
	)
	(port
		(pt 0 128)
		(input)
		(text "PHASEA_IN[15..0]" (rect 0 0 81 12)(font "Arial" ))
		(text "PHASEA_IN[15..0]" (rect 21 123 102 135)(font "Arial" ))
		(line (pt 0 128)(pt 16 128)(line_width 3))
	)
	(port
		(pt 0 144)
		(input)
		(text "PHASEB_IN[15..0]" (rect 0 0 79 12)(font "Arial" ))
		(text "PHASEB_IN[15..0]" (rect 21 139 100 151)(font "Arial" ))
		(line (pt 0 144)(pt 16 144)(line_width 3))
	)
	(port
		(pt 0 160)
		(input)
		(text "CS" (rect 0 0 12 12)(font "Arial" ))
		(text "CS" (rect 21 155 33 167)(font "Arial" ))
		(line (pt 0 160)(pt 16 160)(line_width 1))
	)
	(port
		(pt 0 176)
		(input)
		(text "WR_EN" (rect 0 0 38 12)(font "Arial" ))
		(text "WR_EN" (rect 21 171 59 183)(font "Arial" ))
		(line (pt 0 176)(pt 16 176)(line_width 1))
	)
	(port
		(pt 0 192)
		(input)
		(text "ADDR[15..0]" (rect 0 0 54 12)(font "Arial" ))
		(text "ADDR[15..0]" (rect 21 187 75 199)(font "Arial" ))
		(line (pt 0 192)(pt 16 192)(line_width 3))
	)
	(port
		(pt 360 32)
		(output)
		(text "COUT_A_FINAL[PHASE_WIDTH-1..0]" (rect 0 0 171 12)(font "Arial" ))
		(text "COUT_A_FINAL[PHASE_WIDTH-1..0]" (rect 168 27 339 39)(font "Arial" ))
		(line (pt 360 32)(pt 344 32)(line_width 3))
	)
	(port
		(pt 360 48)
		(output)
		(text "COUT_B_FINAL[PHASE_WIDTH-1..0]" (rect 0 0 168 12)(font "Arial" ))
		(text "COUT_B_FINAL[PHASE_WIDTH-1..0]" (rect 171 43 339 55)(font "Arial" ))
		(line (pt 360 48)(pt 344 48)(line_width 3))
	)
	(port
		(pt 360 64)
		(output)
		(text "FREQ_OUT_A_FINAL" (rect 0 0 103 12)(font "Arial" ))
		(text "FREQ_OUT_A_FINAL" (rect 236 59 339 71)(font "Arial" ))
		(line (pt 360 64)(pt 344 64)(line_width 1))
	)
	(port
		(pt 360 80)
		(output)
		(text "FREQ_OUT_B_FINAL" (rect 0 0 101 12)(font "Arial" ))
		(text "FREQ_OUT_B_FINAL" (rect 238 75 339 87)(font "Arial" ))
		(line (pt 360 80)(pt 344 80)(line_width 1))
	)
	(parameter
		"ADDR10"
		"0000000000001010"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR11"
		"0000000000001011"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"PHASE_WIDTH"
		"8"
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(drawing
		(rectangle (rect 16 16 344 224)(line_width 1))
	)
	(annotation_block (parameter)(rect 376 -64 476 16))
)
