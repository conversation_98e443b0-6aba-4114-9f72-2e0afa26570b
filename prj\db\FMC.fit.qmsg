{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "12 12 " "Parallel compilation is enabled and will use 12 of the 12 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Fitter" 0 -1 1721436036546 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "FMC EP4CE10F17C8 " "Selected device EP4CE10F17C8 for design \"FMC\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1721436036564 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1721436036601 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1721436036601 ""}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|pll1 Cyclone IV E PLL " "Implemented PLL \"MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|wire_pll1_clk\[0\] 3 1 0 0 " "Implementing clock multiplication of 3, clock division of 1, and phase shift of 0 degrees (0 ps) for MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|wire_pll1_clk\[0\] port" {  } { { "db/mypll_altpll.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/mypll_altpll.v" 43 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1074 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 15099 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "Design Software" 0 -1 1721436036628 ""}  } { { "db/mypll_altpll.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/mypll_altpll.v" 43 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1074 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 15535 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "Fitter" 0 -1 1721436036628 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1721436036698 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE6F17C8 " "Device EP4CE6F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1721436036765 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15F17C8 " "Device EP4CE15F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1721436036765 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22F17C8 " "Device EP4CE22F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1721436036765 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1721436036765 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ C1 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location C1" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6390 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1721436036769 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ D2 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location D2" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6392 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1721436036769 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ H1 " "Pin ~ALTERA_DCLK~ is reserved at location H1" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6394 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1721436036769 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ H2 " "Pin ~ALTERA_DATA0~ is reserved at location H2" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6396 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1721436036769 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ F16 " "Pin ~ALTERA_nCEO~ is reserved at location F16" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6398 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1721436036769 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1721436036769 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1721436036770 ""}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 176045 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "Fitter" 0 -1 1721436036790 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "314 " "The Timing Analyzer is analyzing 314 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Fitter" 0 -1 1721436037224 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436037226 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436037226 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1721436037226 ""} { "Info" "ISTA_SDC_STATEMENT_ENTITY" "sld_hub " "Entity sld_hub" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "create_clock -name altera_reserved_tck \[get_ports \{altera_reserved_tck\}\] -period 10MHz    " "create_clock -name altera_reserved_tck \[get_ports \{altera_reserved_tck\}\] -period 10MHz   " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436037226 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_clock_groups -asynchronous -group \{altera_reserved_tck\} " "set_clock_groups -asynchronous -group \{altera_reserved_tck\}" {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436037226 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1721436037226 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Fitter" 0 -1 1721436037226 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "FMC.sdc " "Synopsys Design Constraints File file not found: 'FMC.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Fitter" 0 -1 1721436037234 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a13~porta_address_reg0 FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a13~porta_address_reg0 is being clocked by FREQ_DEV:u_DA1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037238 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037238 "|TOP|FREQ_DEV:u_DA1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "CLK " "Node: CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT CLK " "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT is being clocked by CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037238 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037238 "|TOP|CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FPGA_CS_NEL " "Node: FPGA_CS_NEL was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] FPGA_CS_NEL " "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] is being clocked by FPGA_CS_NEL" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037239 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037239 "|TOP|FPGA_CS_NEL"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a13~porta_address_reg0 FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a13~porta_address_reg0 is being clocked by FREQ_DEV:u_DA2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037239 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037239 "|TOP|FREQ_DEV:u_DA2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD2_INPUT_CLK " "Node: AD2_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst4 AD2_INPUT_CLK " "Register inst4 is being clocked by AD2_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037239 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037239 "|TOP|AD2_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD1_INPUT_CLK " "Node: AD1_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst13 AD1_INPUT_CLK " "Register inst13 is being clocked by AD1_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037239 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037239 "|TOP|AD1_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037239 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037239 "|TOP|FREQ_DEV:u_AD2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436037239 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1721436037239 "|TOP|FREQ_DEV:u_AD1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_GENERIC_WARNING" "PLL cross checking found inconsistent PLL clock settings: " "PLL cross checking found inconsistent PLL clock settings:" { { "Warning" "WSTA_GENERIC_WARNING" "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000 " "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000" {  } {  } 0 332056 "%1!s!" 0 0 "Design Software" 0 -1 1721436037250 ""}  } {  } 0 332056 "%1!s!" 0 0 "Fitter" 0 -1 1721436037250 ""}
{ "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_PARENT" "" "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." { { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Rise) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Rise) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436037250 ""} { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Fall) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Fall) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436037250 ""}  } {  } 1 332168 "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." 0 0 "Fitter" 0 -1 1721436037250 ""}
{ "Info" "ISTA_USER_TDC_OPTIMIZATION_GOALS" "" "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" {  } {  } 0 332129 "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" 0 0 "Fitter" 0 -1 1721436037250 ""}
{ "Info" "ISTA_REPORT_CLOCKS_INFO" "Found 1 clocks " "Found 1 clocks" { { "Info" "ISTA_REPORT_CLOCKS_INFO" "  Period   Clock Name " "  Period   Clock Name" {  } {  } 0 332111 "%1!s!" 0 0 "Design Software" 0 -1 1721436037250 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "======== ============ " "======== ============" {  } {  } 0 332111 "%1!s!" 0 0 "Design Software" 0 -1 1721436037250 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" " 100.000 altera_reserved_tck " " 100.000 altera_reserved_tck" {  } {  } 0 332111 "%1!s!" 0 0 "Design Software" 0 -1 1721436037250 ""}  } {  } 0 332111 "%1!s!" 0 0 "Fitter" 0 -1 1721436037250 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_1) " "Automatically promoted node MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_1)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G3 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G3" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037331 ""}  } { { "db/mypll_altpll.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/mypll_altpll.v" 77 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1074 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037331 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "altera_internal_jtag~TCKUTAP  " "Automatically promoted node altera_internal_jtag~TCKUTAP " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037331 ""}  } { { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 3463 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037331 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "fmc_control:inst\|fmc_rd_en  " "Automatically promoted node fmc_control:inst\|fmc_rd_en " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[0\]~output " "Destination node FPGA_DB\[0\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2516 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[1\]~output " "Destination node FPGA_DB\[1\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2515 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[2\]~output " "Destination node FPGA_DB\[2\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2514 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[3\]~output " "Destination node FPGA_DB\[3\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2513 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[4\]~output " "Destination node FPGA_DB\[4\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2512 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[5\]~output " "Destination node FPGA_DB\[5\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2511 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[6\]~output " "Destination node FPGA_DB\[6\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2510 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[7\]~output " "Destination node FPGA_DB\[7\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2509 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[8\]~output " "Destination node FPGA_DB\[8\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2508 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[9\]~output " "Destination node FPGA_DB\[9\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 232 -312 -112 248 "FPGA_DB\[15..0\]" "" } { 72 640 741 96 "FPGA_DB\[15..0\]" "" } { 216 -112 -11 240 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2507 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Design Software" 0 -1 1721436037331 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1721436037331 ""}  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 59 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1026 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037331 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_AD1_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "AD1_OUTCLK~output " "Destination node AD1_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 872 432 621 888 "AD1_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6300 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1721436037331 ""}  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FREQ_DEV.v" 7 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1508 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037331 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_AD2_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037331 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "AD2_OUTCLK~output " "Destination node AD2_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 872 888 1072 888 "AD2_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6301 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037331 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1721436037331 ""}  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FREQ_DEV.v" 7 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1271 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037331 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_DA1_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_DA1_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037332 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA1_OUTCLK~output " "Destination node DA1_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 952 2992 3181 968 "DA1_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6298 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037332 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1721436037332 ""}  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FREQ_DEV.v" 7 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1160 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037332 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_DA2_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_DA2_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037332 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA2_OUTCLK~output " "Destination node DA2_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 1088 2992 3181 1104 "DA2_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 6299 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1721436037332 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1721436037332 ""}  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FREQ_DEV.v" 7 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 1204 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037332 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_H\[15\]~0  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_H\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037332 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2105 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037332 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_L\[15\]~0  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_L\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037332 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2106 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037332 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD2_FREQ_DATA_H\[15\]~0  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD2_FREQ_DATA_H\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1721436037332 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 0 { 0 ""} 0 2104 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1721436037332 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1721436037580 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1721436037582 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1721436037582 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1721436037585 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1721436037589 ""}
{ "Extra Info" "IFSAC_FSAC_START_MAC_SCAN_CHAIN_INFERENCING" "" "Start inferring scan chains for DSP blocks" {  } {  } 1 176238 "Start inferring scan chains for DSP blocks" 1 0 "Fitter" 0 -1 1721436037593 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_MAC_SCAN_CHAIN_INFERENCING" "" "Inferring scan chains for DSP blocks is complete" {  } {  } 1 176239 "Inferring scan chains for DSP blocks is complete" 1 0 "Fitter" 0 -1 1721436037593 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1721436037596 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 1721436037680 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "No registers were packed into other blocks" {  } {  } 1 176219 "No registers were packed into other blocks" 0 0 "Design Software" 0 -1 1721436037685 ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 1721436037685 ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:01 " "Fitter preparation operations ending: elapsed time is 00:00:01" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1721436037748 ""}
{ "Info" "IVPR20K_VPR_FAMILY_APL_ERROR" "" "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." {  } {  } 0 14896 "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." 0 0 "Fitter" 0 -1 1721436037753 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 1721436038091 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1721436038274 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:01 " "Fitter placement operations ending: elapsed time is 00:00:01" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "4 " "Router estimated average interconnect usage is 4% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "6 X0_Y0 X10_Y11 " "Router estimated peak interconnect usage is 6% of the available device resources in the region that extends from location X0_Y0 to location X10_Y11" {  } { { "loc" "" { Generic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/" { { 1 { 0 "Router estimated peak interconnect usage is 6% of the available device resources in the region that extends from location X0_Y0 to location X10_Y11"} { { 12 { 0 ""} 0 0 11 12 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_TIMING" "" "Optimizations that may affect the design's timing were skipped" {  } {  } 0 170200 "Optimizations that may affect the design's timing were skipped" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "the Fitter 0.33 " "Total time spent on timing analysis during the Fitter is 0.33 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during %1!s! is %2!s! seconds." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1721436040456 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:01 " "Fitter post-fit operations ending: elapsed time is 00:00:01" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1721436040904 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/output_files/FMC.fit.smsg " "Generated suppressed messages file C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/output_files/FMC.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1721436041256 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 16 s Quartus Prime " "Quartus Prime Fitter was successful. 0 errors, 16 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "6154 " "Peak virtual memory: 6154 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1721436041746 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Jul 20 08:40:41 2024 " "Processing ended: Sat Jul 20 08:40:41 2024" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1721436041746 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:05 " "Elapsed time: 00:00:05" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1721436041746 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:03 " "Total CPU time (on all processors): 00:00:03" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1721436041746 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1721436041746 ""}
