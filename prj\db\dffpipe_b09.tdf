--dffpipe DELAY=2 WIDTH=11 clock d q ALTERA_INTERNAL_OPTIONS=AUTO_SHIFT_REGISTER_RECOGNITION=OFF
--VERSION_BEGIN 18.1 cbx_mgl 2018:09:12:13:10:36:SJ cbx_stratixii 2018:09:12:13:04:24:SJ cbx_util_mgl 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.



--synthesis_resources = reg 22 
OPTIONS ALTERA_INTERNAL_OPTION = "AUTO_SHIFT_REGISTER_RECOGNITION=OFF";

SUBDESIGN dffpipe_b09
( 
	clock	:	input;
	d[10..0]	:	input;
	q[10..0]	:	output;
) 
VARIABLE 
	dffe13a[10..0] : dffe;
	dffe14a[10..0] : dffe;
	clrn	: NODE;
	ena	: NODE;
	prn	: NODE;
	sclr	: NODE;

BEGIN 
	dffe13a[].clk = clock;
	dffe13a[].clrn = clrn;
	dffe13a[].d = (d[] & (! sclr));
	dffe13a[].ena = ena;
	dffe13a[].prn = prn;
	dffe14a[].clk = clock;
	dffe14a[].clrn = clrn;
	dffe14a[].d = (dffe13a[].q & (! sclr));
	dffe14a[].ena = ena;
	dffe14a[].prn = prn;
	clrn = VCC;
	ena = VCC;
	prn = VCC;
	q[] = dffe14a[].q;
	sclr = GND;
END;
--VALID FILE
