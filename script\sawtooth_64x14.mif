-- MIF file generated by Python script
-- Waveform: sawtooth
--
WIDTH = 14;
DEPTH = 64;
ADDRESS_RADIX = DEC;
DATA_RADIX = DEC;

CONTENT BEGIN
	0 : 0;
	1 : 260;
	2 : 520;
	3 : 780;
	4 : 1040;
	5 : 1300;
	6 : 1560;
	7 : 1820;
	8 : 2080;
	9 : 2340;
	10 : 2600;
	11 : 2860;
	12 : 3120;
	13 : 3380;
	14 : 3640;
	15 : 3900;
	16 : 4160;
	17 : 4420;
	18 : 4680;
	19 : 4940;
	20 : 5200;
	21 : 5461;
	22 : 5721;
	23 : 5981;
	24 : 6241;
	25 : 6501;
	26 : 6761;
	27 : 7021;
	28 : 7281;
	29 : 7541;
	30 : 7801;
	31 : 8061;
	32 : 8321;
	33 : 8581;
	34 : 8841;
	35 : 9101;
	36 : 9361;
	37 : 9621;
	38 : 9881;
	39 : 10141;
	40 : 10401;
	41 : 10661;
	42 : 10922;
	43 : 11182;
	44 : 11442;
	45 : 11702;
	46 : 11962;
	47 : 12222;
	48 : 12482;
	49 : 12742;
	50 : 13002;
	51 : 13262;
	52 : 13522;
	53 : 13782;
	54 : 14042;
	55 : 14302;
	56 : 14562;
	57 : 14822;
	58 : 15082;
	59 : 15342;
	60 : 15602;
	61 : 15862;
	62 : 16122;
	63 : 16383;
END;
