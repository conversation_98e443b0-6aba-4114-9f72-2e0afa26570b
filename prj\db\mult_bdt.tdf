--lpm_mult CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" DEVICE_FAMILY="Cyclone IV E" DSP_BLOCK_BALANCING="Auto" INPUT_A_IS_CONSTANT="NO" INPUT_B_IS_CONSTANT="NO" LPM_REPRESENTATION="UNSIGNED" LPM_WIDTHA=32 LPM_WIDTHB=16 LPM_WIDTHP=48 LPM_WIDTHS=1 MAXIMIZE_SPEED=5 dataa datab result CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48
--VERSION_BEGIN 18.1 cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_mult 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_padd 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ cbx_util_mgl 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION cycloneive_mac_mult (aclr, clk, dataa[dataa_width-1..0], datab[datab_width-1..0], ena, signa, signb)
WITH ( dataa_clock, dataa_width, datab_clock, datab_width, signa_clock, signb_clock)
RETURNS ( dataout[dataa_width+datab_width-1..0]);
FUNCTION cycloneive_mac_out (aclr, clk, dataa[dataa_width-1..0], ena)
WITH ( dataa_width = 0, output_clock)
RETURNS ( dataout[dataa_width-1..0]);
FUNCTION soft (in)
RETURNS ( out);

--synthesis_resources = dsp_9bit 4 lut 31 
SUBDESIGN mult_bdt
( 
	dataa[31..0]	:	input;
	datab[15..0]	:	input;
	result[47..0]	:	output;
) 
VARIABLE
	add5_result[30..0]	:	WIRE;
	mac_mult1 : cycloneive_mac_mult
		WITH (
			dataa_clock = "none",
			dataa_width = 18,
			datab_clock = "none",
			datab_width = 16,
			signa_clock = "none",
			signb_clock = "none"
		);
	mac_mult3 : cycloneive_mac_mult
		WITH (
			dataa_clock = "none",
			dataa_width = 14,
			datab_clock = "none",
			datab_width = 16,
			signa_clock = "none",
			signb_clock = "none"
		);
	mac_out2 : cycloneive_mac_out
		WITH (
			dataa_width = 34,
			output_clock = "none"
		);
	mac_out4 : cycloneive_mac_out
		WITH (
			dataa_width = 30,
			output_clock = "none"
		);
	sft6a[30..0] : soft;
	sft7a[30..0] : soft;
	sft8a[30..0] : soft;
	w129w	: WIRE;
	w241w[48..0]	: WIRE;

BEGIN 
	add5_result[] = sft6a[].out + sft7a[].out;
	mac_mult1.dataa[] = ( dataa[17..0]);
	mac_mult1.datab[] = ( datab[15..0]);
	mac_mult1.signa = B"0";
	mac_mult1.signb = B"0";
	mac_mult3.dataa[] = ( dataa[31..18]);
	mac_mult3.datab[] = ( datab[15..0]);
	mac_mult3.signa = B"0";
	mac_mult3.signb = B"0";
	mac_out2.dataa[] = mac_mult1.dataout[];
	mac_out4.dataa[] = mac_mult3.dataout[];
	sft6a[].in = ( w129w, ( mac_out4.dataout[29..29], ( mac_out4.dataout[28..28], ( mac_out4.dataout[27..27], ( mac_out4.dataout[26..26], ( mac_out4.dataout[25..25], ( mac_out4.dataout[24..24], ( mac_out4.dataout[23..23], ( mac_out4.dataout[22..22], ( mac_out4.dataout[21..21], ( mac_out4.dataout[20..20], ( mac_out4.dataout[19..19], ( mac_out4.dataout[18..18], ( mac_out4.dataout[17..17], ( mac_out4.dataout[16..16], ( mac_out2.dataout[33..33], ( mac_out2.dataout[32..32], ( mac_out2.dataout[31..31], ( mac_out2.dataout[30..30], ( mac_out2.dataout[29..29], ( mac_out2.dataout[28..28], ( mac_out2.dataout[27..27], ( mac_out2.dataout[26..26], ( mac_out2.dataout[25..25], ( mac_out2.dataout[24..24], ( mac_out2.dataout[23..23], ( mac_out2.dataout[22..22], ( mac_out2.dataout[21..21], ( mac_out2.dataout[20..20], ( mac_out2.dataout[19..18]))))))))))))))))))))))))))))));
	sft7a[].in = ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( w129w, ( mac_out4.dataout[15..15], ( mac_out4.dataout[14..14], ( mac_out4.dataout[13..13], ( mac_out4.dataout[12..12], ( mac_out4.dataout[11..11], ( mac_out4.dataout[10..10], ( mac_out4.dataout[9..9], ( mac_out4.dataout[8..8], ( mac_out4.dataout[7..7], ( mac_out4.dataout[6..6], ( mac_out4.dataout[5..5], ( mac_out4.dataout[4..4], ( mac_out4.dataout[3..3], ( mac_out4.dataout[2..2], ( mac_out4.dataout[1..0]))))))))))))))))))))))))))))));
	sft8a[].in = add5_result[];
	result[47..0] = w241w[47..0];
	w129w = B"0";
	w241w[] = ( sft8a[30..29].out, sft8a[28..27].out, sft8a[26..25].out, sft8a[24..23].out, sft8a[22..21].out, sft8a[20..19].out, sft8a[18..17].out, sft8a[16..15].out, sft8a[14..13].out, sft8a[12..11].out, sft8a[10..9].out, sft8a[8..7].out, sft8a[6..5].out, sft8a[4..3].out, sft8a[2..1].out, sft8a[0..0].out, mac_out2.dataout[17..17], mac_out2.dataout[16..16], mac_out2.dataout[15..15], mac_out2.dataout[14..14], mac_out2.dataout[13..13], mac_out2.dataout[12..12], mac_out2.dataout[11..11], mac_out2.dataout[10..10], mac_out2.dataout[9..9], mac_out2.dataout[8..8], mac_out2.dataout[7..7], mac_out2.dataout[6..6], mac_out2.dataout[5..5], mac_out2.dataout[4..4], mac_out2.dataout[3..3], mac_out2.dataout[2..2], mac_out2.dataout[1..1], mac_out2.dataout[0..0]);
END;
--VALID FILE
