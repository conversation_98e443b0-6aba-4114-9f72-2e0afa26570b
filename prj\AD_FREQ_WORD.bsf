/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 248 192)
	(text "AD_FREQ_WORD" (rect 5 0 93 12)(font "Arial" ))
	(text "inst" (rect 8 160 20 172)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CS" (rect 0 0 12 12)(font "Arial" ))
		(text "CS" (rect 21 27 33 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "WR_EN" (rect 0 0 38 12)(font "Arial" ))
		(text "WR_EN" (rect 21 43 59 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 1))
	)
	(port
		(pt 0 64)
		(input)
		(text "DATA0[15..0]" (rect 0 0 57 12)(font "Arial" ))
		(text "DATA0[15..0]" (rect 21 59 78 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 0 80)
		(input)
		(text "DATA1[15..0]" (rect 0 0 56 12)(font "Arial" ))
		(text "DATA1[15..0]" (rect 21 75 77 87)(font "Arial" ))
		(line (pt 0 80)(pt 16 80)(line_width 3))
	)
	(port
		(pt 0 96)
		(input)
		(text "DATA2[15..0]" (rect 0 0 57 12)(font "Arial" ))
		(text "DATA2[15..0]" (rect 21 91 78 103)(font "Arial" ))
		(line (pt 0 96)(pt 16 96)(line_width 3))
	)
	(port
		(pt 0 112)
		(input)
		(text "DATA3[15..0]" (rect 0 0 57 12)(font "Arial" ))
		(text "DATA3[15..0]" (rect 21 107 78 119)(font "Arial" ))
		(line (pt 0 112)(pt 16 112)(line_width 3))
	)
	(port
		(pt 0 128)
		(input)
		(text "ADDR[15..0]" (rect 0 0 54 12)(font "Arial" ))
		(text "ADDR[15..0]" (rect 21 123 75 135)(font "Arial" ))
		(line (pt 0 128)(pt 16 128)(line_width 3))
	)
	(port
		(pt 232 32)
		(output)
		(text "AD1_OUTH[15..0]" (rect 0 0 75 12)(font "Arial" ))
		(text "AD1_OUTH[15..0]" (rect 136 27 211 39)(font "Arial" ))
		(line (pt 232 32)(pt 216 32)(line_width 3))
	)
	(port
		(pt 232 48)
		(output)
		(text "AD1_OUTL[15..0]" (rect 0 0 74 12)(font "Arial" ))
		(text "AD1_OUTL[15..0]" (rect 137 43 211 55)(font "Arial" ))
		(line (pt 232 48)(pt 216 48)(line_width 3))
	)
	(port
		(pt 232 64)
		(output)
		(text "AD2_OUTH[15..0]" (rect 0 0 76 12)(font "Arial" ))
		(text "AD2_OUTH[15..0]" (rect 135 59 211 71)(font "Arial" ))
		(line (pt 232 64)(pt 216 64)(line_width 3))
	)
	(port
		(pt 232 80)
		(output)
		(text "AD2_OUTL[15..0]" (rect 0 0 75 12)(font "Arial" ))
		(text "AD2_OUTL[15..0]" (rect 136 75 211 87)(font "Arial" ))
		(line (pt 232 80)(pt 216 80)(line_width 3))
	)
	(parameter
		"ADDR6"
		"0000000000000110"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR7"
		"0000000000000111"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR8"
		"0000000000001000"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"ADDR9"
		"0000000000001001"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(drawing
		(rectangle (rect 16 16 216 160)(line_width 1))
	)
	(annotation_block (parameter)(rect 248 -64 348 16))
)
