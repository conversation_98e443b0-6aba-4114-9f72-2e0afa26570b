/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 240 160)
	(text "CNT32" (rect 5 0 34 12)(font "Arial" ))
	(text "inst" (rect 8 128 20 140)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CLR" (rect 0 0 21 12)(font "Arial" ))
		(text "CLR" (rect 21 27 42 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "CLK" (rect 0 0 20 12)(font "Arial" ))
		(text "CLK" (rect 21 43 41 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 1))
	)
	(port
		(pt 0 64)
		(input)
		(text "CLKBASE" (rect 0 0 47 12)(font "Arial" ))
		(text "CLKBASE" (rect 21 59 68 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 1))
	)
	(port
		(pt 0 80)
		(input)
		(text "CLKEN" (rect 0 0 34 12)(font "Arial" ))
		(text "CLKEN" (rect 21 75 55 87)(font "Arial" ))
		(line (pt 0 80)(pt 16 80)(line_width 1))
	)
	(port
		(pt 0 96)
		(input)
		(text "CLKBASEEN" (rect 0 0 61 12)(font "Arial" ))
		(text "CLKBASEEN" (rect 21 91 82 103)(font "Arial" ))
		(line (pt 0 96)(pt 16 96)(line_width 1))
	)
	(port
		(pt 224 32)
		(output)
		(text "Q[31..0]" (rect 0 0 30 12)(font "Arial" ))
		(text "Q[31..0]" (rect 173 27 203 39)(font "Arial" ))
		(line (pt 224 32)(pt 208 32)(line_width 3))
	)
	(port
		(pt 224 48)
		(output)
		(text "QBASE[31..0]" (rect 0 0 57 12)(font "Arial" ))
		(text "QBASE[31..0]" (rect 146 43 203 55)(font "Arial" ))
		(line (pt 224 48)(pt 208 48)(line_width 3))
	)
	(drawing
		(rectangle (rect 16 16 208 128)(line_width 1))
	)
)
