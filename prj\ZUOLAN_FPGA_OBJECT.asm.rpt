Assembler report for ZUOLAN_FPGA_OBJECT
Wed Jul 30 13:45:35 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Assembler Summary
  3. Assembler Settings
  4. Assembler Generated Files
  5. Assembler Device Options: D:/Learn/TI_/project_6_zuolan_v3/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.sof
  6. Assembler Device Options: D:/Learn/TI_/project_6_zuolan_v3/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.pof
  7. Assembler Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+---------------------------------------------------------------+
; Assembler Summary                                             ;
+-----------------------+---------------------------------------+
; Assembler Status      ; Successful - Wed Jul 30 13:45:35 2025 ;
; Revision Name         ; ZUOLAN_FPGA_OBJECT                    ;
; Top-level Entity Name ; TOP                                   ;
; Family                ; Cyclone IV E                          ;
; Device                ; EP4CE10F17C8                          ;
+-----------------------+---------------------------------------+


+----------------------------------+
; Assembler Settings               ;
+--------+---------+---------------+
; Option ; Setting ; Default Value ;
+--------+---------+---------------+


+-------------------------------------------------------------------------+
; Assembler Generated Files                                               ;
+-------------------------------------------------------------------------+
; File Name                                                               ;
+-------------------------------------------------------------------------+
; D:/Learn/TI_/project_6_zuolan_v3/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.sof ;
; D:/Learn/TI_/project_6_zuolan_v3/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.pof ;
+-------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------+
; Assembler Device Options: D:/Learn/TI_/project_6_zuolan_v3/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.sof ;
+----------------+----------------------------------------------------------------------------------+
; Option         ; Setting                                                                          ;
+----------------+----------------------------------------------------------------------------------+
; JTAG usercode  ; 0x001D2149                                                                       ;
; Checksum       ; 0x001D2149                                                                       ;
+----------------+----------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------+
; Assembler Device Options: D:/Learn/TI_/project_6_zuolan_v3/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.pof ;
+--------------------+------------------------------------------------------------------------------+
; Option             ; Setting                                                                      ;
+--------------------+------------------------------------------------------------------------------+
; JTAG usercode      ; 0x00000000                                                                   ;
; Checksum           ; 0x064EDE60                                                                   ;
; Compression Ratio  ; 2                                                                            ;
+--------------------+------------------------------------------------------------------------------+


+--------------------+
; Assembler Messages ;
+--------------------+
Info: *******************************************************************
Info: Running Quartus Prime Assembler
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition
    Info: Processing started: Wed Jul 30 13:45:35 2025
Info: Command: quartus_asm --read_settings_files=off --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT
Info (115031): Writing out detailed assembly data for power analysis
Info (115030): Assembler is generating device programming files
Info: Quartus Prime Assembler was successful. 0 errors, 0 warnings
    Info: Peak virtual memory: 4680 megabytes
    Info: Processing ended: Wed Jul 30 13:45:35 2025
    Info: Elapsed time: 00:00:00
    Info: Total CPU time (on all processors): 00:00:00


