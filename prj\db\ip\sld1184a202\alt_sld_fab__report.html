<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html xmlns="http://www.w3.org/1999/xhtml">
 <head>
  <title>datasheet for alt_sld_fab</title>
  <style type="text/css">
body { font-family:arial ;}
a { text-decoration:underline ; color:#003000 ;}
a:hover { text-decoration:underline ; color:0030f0 ;}
td { padding : 5px ;}
table.topTitle { width:100% ;}
table.topTitle td.l { text-align:left ; font-weight: bold ; font-size:30px ;}
table.topTitle td.r { text-align:right ; font-weight: bold ; font-size:16px ;}
table.blueBar { width : 100% ; border-spacing : 0px ;}
table.blueBar td { background:#0036ff ; font-size:12px ; color : white ; text-align : left ; font-weight : bold ;}
table.blueBar td.l { text-align : left ;}
table.blueBar td.r { text-align : right ;}
table.items { width:100% ; border-collapse:collapse ;}
table.items td.label { font-weight:bold ; font-size:16px ; vertical-align:top ;}
table.items td.mono { font-family:courier ; font-size:12px ; white-space:pre ;}
div.label { font-weight:bold ; font-size:16px ; vertical-align:top ; text-align:center ;}
table.grid { border-collapse:collapse ;}
table.grid td { border:1px solid #bbb ; font-size:12px ;}
body { font-family:arial ;}
table.x { font-family:courier ; border-collapse:collapse ; padding:2px ;}
table.x td { border:1px solid #bbb ;}
td.tableTitle { font-weight:bold ; text-align:center ;}
table.grid { border-collapse:collapse ;}
table.grid td { border:1px solid #bbb ;}
table.grid td.tableTitle { font-weight:bold ; text-align:center ;}
table.mmap { border-collapse:collapse ; text-size:11px ; border:1px solid #d8d8d8 ;}
table.mmap td { border-color:#d8d8d8 ; border-width:1px ; border-style:solid ;}
table.mmap td.empty { border-style:none ; background-color:#f0f0f0 ;}
table.mmap td.slavemodule { text-align:left ; font-size:11px ; border-style:solid solid none solid ;}
table.mmap td.slavem { text-align:right ; font-size:9px ; font-style:italic ; border-style:none solid none solid ;}
table.mmap td.slaveb { text-align:right ; font-size:9px ; font-style:italic ; border-style:none solid solid solid ;}
table.mmap td.mastermodule { text-align:center ; font-size:11px ; border-style:solid solid none solid ;}
table.mmap td.masterlr { text-align:center ; font-size:9px ; font-style:italic ; border-style:none solid solid solid ;}
table.mmap td.masterl { text-align:center ; font-size:9px ; font-style:italic ; border-style:none none solid solid ;}
table.mmap td.masterm { text-align:center ; font-size:9px ; font-style:italic ; border-style:none none solid none ;}
table.mmap td.masterr { text-align:center ; font-size:9px ; font-style:italic ; border-style:none solid solid none ;}
table.mmap td.addr { font-family:courier ; font-size:9px ; text-align:right ;}
table.connectionboxes { border-collapse:separate ; border-spacing:0px ; font-family:arial ;}
table.connectionboxes td.from { border-bottom:1px solid black ; font-size:9px ; font-style:italic ; vertical-align:bottom ; text-align:left ;}
table.connectionboxes td.to { font-size:9px ; font-style:italic ; vertical-align:top ; text-align:right ;}
table.connectionboxes td.lefthandwire { border-bottom:1px solid black ; font-size:9px ; font-style:italic ; vertical-align:bottom ; text-align:right ;}
table.connectionboxes td.righthandwire { border-bottom:1px solid black ; font-size:9px ; font-style:italic ; vertical-align:bottom ; text-align:left ;}
table.connectionboxes td.righthandlabel { font-size:11px ; vertical-align:bottom ; text-align:left ;}
table.connectionboxes td.neighbor { padding:3px ; border:1px solid black ; font-size: 11px ; background:#e8e8e8 ; vertical-align:center ; text-align:center ;}
table.connectionboxes td.main { padding:8px ; border:1px solid black ; font-size: 14px ; font-weight:bold ; background:#ffffff ; vertical-align:center ; text-align:center ;}
.parametersbox { border:1px solid #d0d0d0 ; display:inline-block ; max-height:160px ; overflow:auto ; width:360px ; font-size:10px ;}
.flowbox { display:inline-block ;}
.parametersbox table { font-size:10px ;}
td.parametername { font-style:italic ;}
td.parametervalue { font-weight:bold ;}
div.greydiv { vertical-align:top ; text-align:center ; background:#eeeeee ; border-top:1px solid #707070 ; border-bottom:1px solid #707070 ; padding:20px ; margin:20px ; width:auto ;}</style>
 </head>
 <body>
  <table class="topTitle">
   <tr>
    <td class="l">alt_sld_fab</td>
    <td class="r">
     <br/>
     <br/>
    </td>
   </tr>
  </table>
  <table class="blueBar">
   <tr>
    <td class="l">2025.07.17.15:34:10</td>
    <td class="r">Datasheet</td>
   </tr>
  </table>
  <div style="width:100% ;  height:10px"> </div>
  <div class="label">Overview</div>
  <div class="greydiv">
   <div style="display:inline-block ; text-align:left">
    <table class="connectionboxes">
     <tr style="height:6px">
      <td></td>
     </tr>
    </table>
   </div><span style="display:inline-block ; width:28px"> </span>
   <div style="display:inline-block ; text-align:left"><span>
     <br/></span>
   </div>
  </div>
  <div style="width:100% ;  height:10px"> </div>
  <div class="label">Memory Map</div>
  <table class="mmap">
   <tr>
    <td class="empty" rowspan="2"></td>
   </tr>
  </table>
  <a name="module_alt_sld_fab"> </a>
  <div>
   <hr/>
   <h2>alt_sld_fab</h2>alt_sld_fab v18.1
   <br/>
   <br/>
   <br/>
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Parameters</h2>
      <table>
       <tr>
        <td class="parametername">DESIGN_HASH</td>
        <td class="parametervalue">0c9bddac927be960db37</td>
       </tr>
       <tr>
        <td class="parametername">NODE_COUNT</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">MAX_WIDTH</td>
        <td class="parametervalue">33</td>
       </tr>
       <tr>
        <td class="parametername">SETTINGS</td>
        <td class="parametervalue">{fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 psig 9b67919e} </td>
       </tr>
       <tr>
        <td class="parametername">CLOCKS</td>
        <td class="parametervalue">{id {} } </td>
       </tr>
       <tr>
        <td class="parametername">AGENTS</td>
        <td class="parametervalue"></td>
       </tr>
       <tr>
        <td class="parametername">EP_INFOS</td>
        <td class="parametervalue">{hpath {sld_signaltap:auto_signaltap_0} } </td>
       </tr>
       <tr>
        <td class="parametername">MIRROR</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">TOP_HUB</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">COMPOSED_SETTINGS</td>
        <td class="parametervalue">{fabric sld dir agent mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } </td>
       </tr>
       <tr>
        <td class="parametername">DEVICE_FAMILY</td>
        <td class="parametervalue">CYCLONEIVE</td>
       </tr>
       <tr>
        <td class="parametername">AUTO_DEVICE</td>
        <td class="parametervalue">Unknown</td>
       </tr>
       <tr>
        <td class="parametername">AUTO_DEVICE_SPEEDGRADE</td>
        <td class="parametervalue">Unknown</td>
       </tr>
       <tr>
        <td class="parametername">deviceFamily</td>
        <td class="parametervalue">UNKNOWN</td>
       </tr>
       <tr>
        <td class="parametername">generateLegacySim</td>
        <td class="parametervalue">false</td>
       </tr>
      </table>
     </td>
    </tr>
   </table>&#160;&#160;
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Software Assignments</h2>(none)</td>
    </tr>
   </table>
  </div>
  <a name="module_alt_sld_fab_presplit"> </a>
  <div>
   <hr/>
   <h2>alt_sld_fab_presplit</h2>altera_super_splitter v18.1
   <br/>
   <br/>
   <br/>
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Parameters</h2>
      <table>
       <tr>
        <td class="parametername">MAX_WIDTH</td>
        <td class="parametervalue">33</td>
       </tr>
       <tr>
        <td class="parametername">SEND_WIDTHS</td>
        <td class="parametervalue">12</td>
       </tr>
       <tr>
        <td class="parametername">RECEIVE_WIDTHS</td>
        <td class="parametervalue">33</td>
       </tr>
       <tr>
        <td class="parametername">deviceFamily</td>
        <td class="parametervalue">UNKNOWN</td>
       </tr>
       <tr>
        <td class="parametername">generateLegacySim</td>
        <td class="parametervalue">false</td>
       </tr>
      </table>
     </td>
    </tr>
   </table>&#160;&#160;
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Software Assignments</h2>(none)</td>
    </tr>
   </table>
  </div>
  <a name="module_alt_sld_fab_splitter"> </a>
  <div>
   <hr/>
   <h2>alt_sld_fab_splitter</h2>altera_sld_splitter v18.1
   <br/>
   <div class="greydiv">
    <table class="connectionboxes">
     <tr>
      <td class="neighbor" rowspan="2">
       <a href="#module_alt_sld_fab_presplit">alt_sld_fab_presplit</a>
      </td>
      <td class="from">pass&#160;&#160;</td>
      <td class="main" rowspan="7">alt_sld_fab_splitter</td>
     </tr>
     <tr>
      <td class="to">&#160;&#160;nodes</td>
     </tr>
     <tr style="height:6px">
      <td></td>
     </tr>
     <tr>
      <td class="neighbor" rowspan="4">
       <a href="#module_alt_sld_fab_sldfabric">alt_sld_fab_sldfabric</a>
      </td>
      <td class="from">clock_0&#160;&#160;</td>
     </tr>
     <tr>
      <td class="to">&#160;&#160;clock_0</td>
     </tr>
     <tr>
      <td class="from">node_0&#160;&#160;</td>
     </tr>
     <tr>
      <td class="to">&#160;&#160;node_0</td>
     </tr>
    </table>
   </div>
   <br/>
   <br/>
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Parameters</h2>
      <table>
       <tr>
        <td class="parametername">FRAGMENTS</td>
        <td class="parametervalue">{{name clock type clock dir end ports { {tck clk in 1 0} } } {name node type conduit dir end ports { {tms tms in 1 1} {tdi tdi in 1 2} {tdo tdo out 1 0} {ena ena in 1 3} {usr1 usr1 in 1 4} {clr clr in 1 5} {clrn clrn in 1 6} {jtag_state_tlr jtag_state_tlr in 1 7} {jtag_state_rti jtag_state_rti in 1 8} {jtag_state_sdrs jtag_state_sdrs in 1 9} {jtag_state_cdr jtag_state_cdr in 1 10} {jtag_state_sdr jtag_state_sdr in 1 11} {jtag_state_e1dr jtag_state_e1dr in 1 12} {jtag_state_pdr jtag_state_pdr in 1 13} {jtag_state_e2dr jtag_state_e2dr in 1 14} {jtag_state_udr jtag_state_udr in 1 15} {jtag_state_sirs jtag_state_sirs in 1 16} {jtag_state_cir jtag_state_cir in 1 17} {jtag_state_sir jtag_state_sir in 1 18} {jtag_state_e1ir jtag_state_e1ir in 1 19} {jtag_state_pir jtag_state_pir in 1 20} {jtag_state_e2ir jtag_state_e2ir in 1 21} {jtag_state_uir jtag_state_uir in 1 22} {ir_in ir_in in 10 23} {irq irq out 1 1} {ir_out ir_out out 10 2} } clock clock assign {debug.controlledBy {link_0} } moduleassign {debug.virtualInterface.link_0 {debug.endpointLink {fabric sld index 1} } } } } </td>
       </tr>
       <tr>
        <td class="parametername">EXAMPLE</td>
        <td class="parametervalue"></td>
       </tr>
       <tr>
        <td class="parametername">ADD_INTERFACE_ASGN</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">deviceFamily</td>
        <td class="parametervalue">UNKNOWN</td>
       </tr>
       <tr>
        <td class="parametername">generateLegacySim</td>
        <td class="parametervalue">false</td>
       </tr>
      </table>
     </td>
    </tr>
   </table>&#160;&#160;
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Software Assignments</h2>(none)</td>
    </tr>
   </table>
  </div>
  <a name="module_alt_sld_fab_jtagpins"> </a>
  <div>
   <hr/>
   <h2>alt_sld_fab_jtagpins</h2>altera_jtag_pins_bridge v18.1
   <br/>
   <br/>
   <br/>
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Parameters</h2>
      <table>
       <tr>
        <td class="parametername">deviceFamily</td>
        <td class="parametervalue">UNKNOWN</td>
       </tr>
       <tr>
        <td class="parametername">generateLegacySim</td>
        <td class="parametervalue">false</td>
       </tr>
      </table>
     </td>
    </tr>
   </table>&#160;&#160;
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Software Assignments</h2>(none)</td>
    </tr>
   </table>
  </div>
  <a name="module_alt_sld_fab_sldfabric"> </a>
  <div>
   <hr/>
   <h2>alt_sld_fab_sldfabric</h2>altera_sld_jtag_hub v18.1
   <br/>
   <div class="greydiv">
    <table class="connectionboxes">
     <tr>
      <td class="neighbor" rowspan="4">
       <a href="#module_alt_sld_fab_jtagpins">alt_sld_fab_jtagpins</a>
      </td>
      <td class="from">clock&#160;&#160;</td>
      <td class="main" rowspan="11">alt_sld_fab_sldfabric</td>
     </tr>
     <tr>
      <td class="to">&#160;&#160;clock</td>
     </tr>
     <tr>
      <td class="from">node&#160;&#160;</td>
     </tr>
     <tr>
      <td class="to">&#160;&#160;node</td>
     </tr>
     <tr>
      <td></td>
      <td></td>
      <td class="from">clock_0&#160;&#160;</td>
      <td class="neighbor" rowspan="4">
       <a href="#module_alt_sld_fab_splitter">alt_sld_fab_splitter</a>
      </td>
     </tr>
     <tr>
      <td></td>
      <td></td>
      <td class="to">&#160;&#160;clock_0</td>
     </tr>
     <tr>
      <td></td>
      <td></td>
      <td class="from">node_0&#160;&#160;</td>
     </tr>
     <tr>
      <td></td>
      <td></td>
      <td class="to">&#160;&#160;node_0</td>
     </tr>
     <tr style="height:6px">
      <td></td>
     </tr>
     <tr>
      <td></td>
      <td></td>
      <td class="from">ident&#160;&#160;</td>
      <td class="neighbor" rowspan="2">
       <a href="#module_alt_sld_fab_ident">alt_sld_fab_ident</a>
      </td>
     </tr>
     <tr>
      <td></td>
      <td></td>
      <td class="to">&#160;&#160;ident_0</td>
     </tr>
    </table>
   </div>
   <br/>
   <br/>
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Parameters</h2>
      <table>
       <tr>
        <td class="parametername">DEVICE_FAMILY</td>
        <td class="parametervalue">CYCLONEIVE</td>
       </tr>
       <tr>
        <td class="parametername">SETTINGS</td>
        <td class="parametervalue">{mfr_code 110 type_code 0 version 6 instance 0 ir_width 10 bridge_agent 0 prefer_host {} } </td>
       </tr>
       <tr>
        <td class="parametername">COUNT</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">N_SEL_BITS</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">N_NODE_IR_BITS</td>
        <td class="parametervalue">10</td>
       </tr>
       <tr>
        <td class="parametername">NODE_INFO</td>
        <td class="parametervalue">00110000000000000110111000000000</td>
       </tr>
       <tr>
        <td class="parametername">COMPILATION_MODE</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">BROADCAST_FEATURE</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">FORCE_IR_CAPTURE_FEATURE</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">FORCE_PRE_1_4_FEATURE</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">NEGEDGE_TDO_LATCH</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">ENABLE_SOFT_CORE_CONTROLLER</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">TOP_HUB</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">CONN_INDEX</td>
        <td class="parametervalue">0</td>
       </tr>
       <tr>
        <td class="parametername">deviceFamily</td>
        <td class="parametervalue">UNKNOWN</td>
       </tr>
       <tr>
        <td class="parametername">generateLegacySim</td>
        <td class="parametervalue">false</td>
       </tr>
      </table>
     </td>
    </tr>
   </table>&#160;&#160;
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Software Assignments</h2>(none)</td>
    </tr>
   </table>
  </div>
  <a name="module_alt_sld_fab_ident"> </a>
  <div>
   <hr/>
   <h2>alt_sld_fab_ident</h2>altera_connection_identification_hub v18.1
   <br/>
   <div class="greydiv">
    <table class="connectionboxes">
     <tr>
      <td class="neighbor" rowspan="2">
       <a href="#module_alt_sld_fab_sldfabric">alt_sld_fab_sldfabric</a>
      </td>
      <td class="from">ident&#160;&#160;</td>
      <td class="main" rowspan="2">alt_sld_fab_ident</td>
     </tr>
     <tr>
      <td class="to">&#160;&#160;ident_0</td>
     </tr>
    </table>
   </div>
   <br/>
   <br/>
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Parameters</h2>
      <table>
       <tr>
        <td class="parametername">DESIGN_HASH</td>
        <td class="parametervalue">0c9bddac927be960db37</td>
       </tr>
       <tr>
        <td class="parametername">COUNT</td>
        <td class="parametervalue">1</td>
       </tr>
       <tr>
        <td class="parametername">SETTINGS</td>
        <td class="parametervalue">{width 4 latency 0} </td>
       </tr>
       <tr>
        <td class="parametername">deviceFamily</td>
        <td class="parametervalue">UNKNOWN</td>
       </tr>
       <tr>
        <td class="parametername">generateLegacySim</td>
        <td class="parametervalue">false</td>
       </tr>
      </table>
     </td>
    </tr>
   </table>&#160;&#160;
   <table class="flowbox">
    <tr>
     <td class="parametersbox">
      <h2>Software Assignments</h2>(none)</td>
    </tr>
   </table>
  </div>
  <table class="blueBar">
   <tr>
    <td class="l">generation took 0.00 seconds</td>
    <td class="r">rendering took 0.02 seconds</td>
   </tr>
  </table>
 </body>
</html>
