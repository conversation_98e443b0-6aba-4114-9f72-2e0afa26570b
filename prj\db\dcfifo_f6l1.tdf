--dcfifo_mixed_widths CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48 DEVICE_FAMILY="Cyclone V" IGNORE_CARRY_BUFFERS="OFF" LPM_NUMWORDS=1024 LPM_SHOWAHEAD="OFF" LPM_WIDTH=12 LPM_WIDTH_R=12 LPM_WIDTHU=10 LPM_WIDTHU_R=10 OVERFLOW_CHECKING="ON" RDSYNC_DELAYPIPE=4 UNDERFLOW_CHECKING="ON" USE_EAB="ON" WRSYNC_DELAYPIPE=4 data q rdclk rdreq wrclk wrfull wrreq ACF_BLOCK_RAM_AND_MLAB_EQUIVALENT_PAUSED_READ_CAPABILITIES="CARE" CYCLONEII_M4K_COMPATIBILITY="ON" INTENDED_DEVICE_FAMILY="Cyclone IV E" LOW_POWER_MODE="AUTO" ALTERA_INTERNAL_OPTIONS=AUTO_SHIFT_REGISTER_RECOGNITION=OFF
--VERSION_BEGIN 18.1 cbx_a_gray2bin 2018:09:12:13:04:24:SJ cbx_a_graycounter 2018:09:12:13:04:24:SJ cbx_altdpram 2018:09:12:13:04:24:SJ cbx_altera_counter 2018:09:12:13:04:24:SJ cbx_altera_gray_counter 2018:09:12:13:04:24:SJ cbx_altera_syncram 2018:09:12:13:04:24:SJ cbx_altera_syncram_nd_impl 2018:09:12:13:04:24:SJ cbx_altsyncram 2018:09:12:13:04:24:SJ cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_dcfifo 2018:09:12:13:04:24:SJ cbx_fifo_common 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_compare 2018:09:12:13:04:24:SJ cbx_lpm_counter 2018:09:12:13:04:24:SJ cbx_lpm_decode 2018:09:12:13:04:24:SJ cbx_lpm_mux 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_scfifo 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ cbx_stratixiii 2018:09:12:13:04:24:SJ cbx_stratixv 2018:09:12:13:04:24:SJ cbx_util_mgl 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION a_graycounter_mh6 (clock, cnt_en)
RETURNS ( q[10..0]);
FUNCTION a_graycounter_ivb (clock, cnt_en)
RETURNS ( q[10..0]);
FUNCTION altsyncram_ska1 (address_a[9..0], address_b[9..0], addressstall_b, clock0, clock1, clocken1, data_a[11..0], wren_a)
RETURNS ( q_b[11..0]);
FUNCTION alt_synch_pipe_sal (clock, d[10..0])
RETURNS ( q[10..0]);
FUNCTION alt_synch_pipe_tal (clock, d[10..0])
RETURNS ( q[10..0]);
FUNCTION cmpr_a06 (dataa[10..0], datab[10..0])
RETURNS ( aeb);

--synthesis_resources = M10K 2 reg 105 
OPTIONS ALTERA_INTERNAL_OPTION = "AUTO_SHIFT_REGISTER_RECOGNITION=OFF;REMOVE_DUPLICATE_REGISTERS=OFF;SYNCHRONIZER_IDENTIFICATION=OFF;SYNCHRONIZATION_REGISTER_CHAIN_LENGTH = 2;suppress_da_rule_internal=d101;suppress_da_rule_internal=d102;suppress_da_rule_internal=d103;{-to wrptr_g} suppress_da_rule_internal=S102;{-to wrptr_g} POWER_UP_LEVEL=LOW;-name CUT ON -from rdptr_g -to ws_dgrp|dffpipe_e09:dffpipe15|dffe16a;-name SDC_STATEMENT ""set_false_path -from *rdptr_g* -to *ws_dgrp|dffpipe_e09:dffpipe15|dffe16a* "";-name CUT ON -from delayed_wrptr_g -to rs_dgwp|dffpipe_d09:dffpipe12|dffe13a;-name SDC_STATEMENT ""set_false_path -from *delayed_wrptr_g* -to *rs_dgwp|dffpipe_d09:dffpipe12|dffe13a* """;

SUBDESIGN dcfifo_f6l1
( 
	data[11..0]	:	input;
	q[11..0]	:	output;
	rdclk	:	input;
	rdreq	:	input;
	wrclk	:	input;
	wrfull	:	output;
	wrreq	:	input;
) 
VARIABLE 
	rdptr_g1p : a_graycounter_mh6;
	wrptr_g1p : a_graycounter_ivb;
	fifo_ram : altsyncram_ska1;
	delayed_wrptr_g[10..0] : dffe;
	rdptr_g[10..0] : dffe;
	wrptr_g[10..0] : dffe
		WITH (
			power_up = "low"
		);
	rs_dgwp : alt_synch_pipe_sal;
	ws_dgrp : alt_synch_pipe_tal;
	rdempty_eq_comp : cmpr_a06;
	wrfull_eq_comp : cmpr_a06;
	int_rdempty	: WIRE;
	int_wrfull	: WIRE;
	ram_address_a[9..0]	: WIRE;
	ram_address_b[9..0]	: WIRE;
	valid_rdreq	: WIRE;
	valid_wrreq	: WIRE;
	wrptr_gs[10..0]	: WIRE;

BEGIN 
	rdptr_g1p.clock = rdclk;
	rdptr_g1p.cnt_en = valid_rdreq;
	wrptr_g1p.clock = wrclk;
	wrptr_g1p.cnt_en = valid_wrreq;
	fifo_ram.address_a[] = ram_address_a[];
	fifo_ram.address_b[] = ram_address_b[];
	fifo_ram.addressstall_b = (! valid_rdreq);
	fifo_ram.clock0 = wrclk;
	fifo_ram.clock1 = rdclk;
	fifo_ram.clocken1 = valid_rdreq;
	fifo_ram.data_a[] = data[];
	fifo_ram.wren_a = valid_wrreq;
	delayed_wrptr_g[].clk = wrclk;
	delayed_wrptr_g[].d = wrptr_g[].q;
	rdptr_g[].clk = rdclk;
	rdptr_g[].d = rdptr_g1p.q[];
	rdptr_g[].ena = valid_rdreq;
	wrptr_g[].clk = wrclk;
	wrptr_g[].d = wrptr_g1p.q[];
	wrptr_g[].ena = valid_wrreq;
	rs_dgwp.clock = rdclk;
	rs_dgwp.d[] = delayed_wrptr_g[].q;
	ws_dgrp.clock = wrclk;
	ws_dgrp.d[] = rdptr_g[].q;
	rdempty_eq_comp.dataa[] = rs_dgwp.q[];
	rdempty_eq_comp.datab[] = rdptr_g[].q;
	wrfull_eq_comp.dataa[] = ws_dgrp.q[];
	wrfull_eq_comp.datab[] = wrptr_gs[];
	int_rdempty = rdempty_eq_comp.aeb;
	int_wrfull = wrfull_eq_comp.aeb;
	q[] = fifo_ram.q_b[];
	ram_address_a[] = ( (wrptr_g[10..10].q $ wrptr_g[9..9].q), wrptr_g[8..0].q);
	ram_address_b[] = ( (rdptr_g1p.q[10..10] $ rdptr_g1p.q[9..9]), rdptr_g1p.q[8..0]);
	valid_rdreq = (rdreq & (! int_rdempty));
	valid_wrreq = (wrreq & (! int_wrfull));
	wrfull = int_wrfull;
	wrptr_gs[] = ( (! wrptr_g[10..10].q), (! wrptr_g[9..9].q), wrptr_g[8..0].q);
END;
--VALID FILE
