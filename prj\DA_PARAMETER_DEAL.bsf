/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 320 256)
	(text "DA_PARAMETER_DEAL" (rect 5 0 127 12)(font "Arial" ))
	(text "inst" (rect 8 224 20 236)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CS" (rect 0 0 12 12)(font "Arial" ))
		(text "CS" (rect 21 27 33 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "WR_EN" (rect 0 0 38 12)(font "Arial" ))
		(text "WR_EN" (rect 21 43 59 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 1))
	)
	(port
		(pt 0 64)
		(input)
		(text "DA1_FREQ_H[15..0]" (rect 0 0 89 12)(font "Arial" ))
		(text "DA1_FREQ_H[15..0]" (rect 21 59 110 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 0 80)
		(input)
		(text "DA1_FREQ_L[15..0]" (rect 0 0 88 12)(font "Arial" ))
		(text "DA1_FREQ_L[15..0]" (rect 21 75 109 87)(font "Arial" ))
		(line (pt 0 80)(pt 16 80)(line_width 3))
	)
	(port
		(pt 0 96)
		(input)
		(text "DA2_FREQ_H[15..0]" (rect 0 0 90 12)(font "Arial" ))
		(text "DA2_FREQ_H[15..0]" (rect 21 91 111 103)(font "Arial" ))
		(line (pt 0 96)(pt 16 96)(line_width 3))
	)
	(port
		(pt 0 112)
		(input)
		(text "DA2_FREQ_L[15..0]" (rect 0 0 89 12)(font "Arial" ))
		(text "DA2_FREQ_L[15..0]" (rect 21 107 110 119)(font "Arial" ))
		(line (pt 0 112)(pt 16 112)(line_width 3))
	)
	(port
		(pt 0 128)
		(input)
		(text "WAVE_SEL_IN[15..0]" (rect 0 0 97 12)(font "Arial" ))
		(text "WAVE_SEL_IN[15..0]" (rect 21 123 118 135)(font "Arial" ))
		(line (pt 0 128)(pt 16 128)(line_width 3))
	)
	(port
		(pt 0 144)
		(input)
		(text "DA_STEP_IN[15..0]" (rect 0 0 84 12)(font "Arial" ))
		(text "DA_STEP_IN[15..0]" (rect 21 139 105 151)(font "Arial" ))
		(line (pt 0 144)(pt 16 144)(line_width 3))
	)
	(port
		(pt 0 160)
		(input)
		(text "DA1_AMP_IN[15..0]" (rect 0 0 86 12)(font "Arial" ))
		(text "DA1_AMP_IN[15..0]" (rect 21 155 107 167)(font "Arial" ))
		(line (pt 0 160)(pt 16 160)(line_width 3))
	)
	(port
		(pt 0 176)
		(input)
		(text "DA2_AMP_IN[15..0]" (rect 0 0 87 12)(font "Arial" ))
		(text "DA2_AMP_IN[15..0]" (rect 21 171 108 183)(font "Arial" ))
		(line (pt 0 176)(pt 16 176)(line_width 3))
	)
	(port
		(pt 0 192)
		(input)
		(text "ADDR[15..0]" (rect 0 0 54 12)(font "Arial" ))
		(text "ADDR[15..0]" (rect 21 187 75 199)(font "Arial" ))
		(line (pt 0 192)(pt 16 192)(line_width 3))
	)
	(port
		(pt 304 32)
		(output)
		(text "DA1_OUTH[15..0]" (rect 0 0 75 12)(font "Arial" ))
		(text "DA1_OUTH[15..0]" (rect 208 27 283 39)(font "Arial" ))
		(line (pt 304 32)(pt 288 32)(line_width 3))
	)
	(port
		(pt 304 48)
		(output)
		(text "DA1_OUTL[15..0]" (rect 0 0 74 12)(font "Arial" ))
		(text "DA1_OUTL[15..0]" (rect 209 43 283 55)(font "Arial" ))
		(line (pt 304 48)(pt 288 48)(line_width 3))
	)
	(port
		(pt 304 64)
		(output)
		(text "DA2_OUTH[15..0]" (rect 0 0 76 12)(font "Arial" ))
		(text "DA2_OUTH[15..0]" (rect 207 59 283 71)(font "Arial" ))
		(line (pt 304 64)(pt 288 64)(line_width 3))
	)
	(port
		(pt 304 80)
		(output)
		(text "DA2_OUTL[15..0]" (rect 0 0 75 12)(font "Arial" ))
		(text "DA2_OUTL[15..0]" (rect 208 75 283 87)(font "Arial" ))
		(line (pt 304 80)(pt 288 80)(line_width 3))
	)
	(port
		(pt 304 96)
		(output)
		(text "DA1_AMP_OUT[11..0]" (rect 0 0 95 12)(font "Arial" ))
		(text "DA1_AMP_OUT[11..0]" (rect 188 91 283 103)(font "Arial" ))
		(line (pt 304 96)(pt 288 96)(line_width 3))
	)
	(port
		(pt 304 112)
		(output)
		(text "DA2_AMP_OUT[11..0]" (rect 0 0 96 12)(font "Arial" ))
		(text "DA2_AMP_OUT[11..0]" (rect 187 107 283 119)(font "Arial" ))
		(line (pt 304 112)(pt 288 112)(line_width 3))
	)
	(port
		(pt 304 128)
		(output)
		(text "DA1_STEP_OUT[7..0]" (rect 0 0 95 12)(font "Arial" ))
		(text "DA1_STEP_OUT[7..0]" (rect 188 123 283 135)(font "Arial" ))
		(line (pt 304 128)(pt 288 128)(line_width 3))
	)
	(port
		(pt 304 144)
		(output)
		(text "DA2_STEP_OUT[7..0]" (rect 0 0 96 12)(font "Arial" ))
		(text "DA2_STEP_OUT[7..0]" (rect 187 139 283 151)(font "Arial" ))
		(line (pt 304 144)(pt 288 144)(line_width 3))
	)
	(port
		(pt 304 160)
		(output)
		(text "DA1_WAVE_OUT[7..0]" (rect 0 0 105 12)(font "Arial" ))
		(text "DA1_WAVE_OUT[7..0]" (rect 178 155 283 167)(font "Arial" ))
		(line (pt 304 160)(pt 288 160)(line_width 3))
	)
	(port
		(pt 304 176)
		(output)
		(text "DA2_WAVE_OUT[7..0]" (rect 0 0 106 12)(font "Arial" ))
		(text "DA2_WAVE_OUT[7..0]" (rect 177 171 283 183)(font "Arial" ))
		(line (pt 304 176)(pt 288 176)(line_width 3))
	)
	(parameter
		"DA1_FREQ_H_ADDR2"
		"0000000000000010"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"DA1_FREQ_L_ADDR3"
		"0000000000000011"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"DA2_FREQ_H_ADDR4"
		"0000000000000100"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"DA2_FREQ_L_ADDR5"
		"0000000000000101"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"WAVE_SEL_ADDR12"
		"0000000000001100"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"DA_STEP_ADDR13"
		"0000000000001101"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"DA1_AMP_ADDR14"
		"0000000000001110"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(parameter
		"DA2_AMP_ADDR15"
		"0000000000001111"
		""
		(type "PARAMETER_UNSIGNED_BIN")	)
	(drawing
		(rectangle (rect 16 16 288 224)(line_width 1))
	)
	(annotation_block (parameter)(rect 320 -64 420 16))
)
