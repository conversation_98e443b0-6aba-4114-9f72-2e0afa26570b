/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 248 128)
	(text "FREQ_DEV" (rect 5 0 61 12)(font "Arial" ))
	(text "inst" (rect 8 96 20 108)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "CLK" (rect 0 0 20 12)(font "Arial" ))
		(text "CLK" (rect 21 27 41 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "EN" (rect 0 0 14 12)(font "Arial" ))
		(text "EN" (rect 21 43 35 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 1))
	)
	(port
		(pt 0 64)
		(input)
		(text "FREQH_W[15..0]" (rect 0 0 75 12)(font "Arial" ))
		(text "FREQH_W[15..0]" (rect 21 59 96 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 0 80)
		(input)
		(text "FREQL_W[15..0]" (rect 0 0 74 12)(font "Arial" ))
		(text "FREQL_W[15..0]" (rect 21 75 95 87)(font "Arial" ))
		(line (pt 0 80)(pt 16 80)(line_width 3))
	)
	(port
		(pt 232 32)
		(output)
		(text "FREQ_OUT" (rect 0 0 54 12)(font "Arial" ))
		(text "FREQ_OUT" (rect 157 27 211 39)(font "Arial" ))
		(line (pt 232 32)(pt 216 32)(line_width 1))
	)
	(drawing
		(rectangle (rect 16 16 216 96)(line_width 1))
	)
)
