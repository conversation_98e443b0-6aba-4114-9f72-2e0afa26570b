--lpm_add_sub CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48 DEVICE_FAMILY="Cyclone IV E" LPM_DIRECTION="SUB" LPM_WIDTH=1 cout dataa datab result
--VERSION_BEGIN 18.1 cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.



--synthesis_resources = 
SUBDESIGN add_sub_7pc
( 
	cout	:	output;
	dataa[0..0]	:	input;
	datab[0..0]	:	input;
	result[0..0]	:	output;
) 
VARIABLE 
	carry_eqn[0..0]	: WIRE;
	cin_wire	: WIRE;
	datab_node[0..0]	: WIRE;
	sum_eqn[0..0]	: WIRE;

BEGIN 
	carry_eqn[] = ( ((dataa[0..0] & datab_node[0..0]) # ((dataa[0..0] # datab_node[0..0]) & cin_wire)));
	cin_wire = B"1";
	cout = carry_eqn[0..0];
	datab_node[] = (! datab[]);
	result[] = sum_eqn[];
	sum_eqn[] = ( ((dataa[0..0] $ datab_node[0..0]) $ cin_wire));
END;
--VALID FILE
